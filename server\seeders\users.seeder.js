import bcrypt from 'bcryptjs';

export default {
  async up(queryInterface, Sequelize) {
    const hashedPassword = await bcrypt.hash('admin@123', 8);

    const superAdmin = {
      tenant_id: 1, // ✅ must exist in tenants table
      organization_id: null, // or set an org id if required
      name: 'Super Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role_id: 1, // ✅ must exist in roles table
      phone_number: null,
      department: null,
      position: null,
      avatar: null,
      last_login: null,
      created_at: new Date(),
      updated_at: new Date(),
      created_by: null,
      updated_by: null,
      is_deleted: false,
      is_active: true,
    };

    await queryInterface.bulkInsert('users', [superAdmin]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('users', { email: '<EMAIL>' }, {});
  }
};
