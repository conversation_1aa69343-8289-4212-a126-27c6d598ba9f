import bcrypt from 'bcryptjs';

export default {
  async up(queryInterface, Sequelize) {
    const hashedPassword = await bcrypt.hash('admin@123', 8);

    const superAdmin = {
      name: 'admin',
      role_id: 1,
      email: '<EMAIL>',
      password: hashedPassword, // ✅ already resolved
      phone_number: null,
      last_login: null,
      created_at: new Date(),
      updated_at: new Date(),
      created_by: null, // First user, so no creator
      updated_by: null,
      is_deleted: false,
      is_active: true,
    };

    await queryInterface.bulkInsert('users', [superAdmin]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('users', { email: '<EMAIL>' }, {});
  }
};
