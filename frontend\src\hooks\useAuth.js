'use client';

import { useState, useEffect, createContext, useContext } from 'react';
import { useRouter } from 'next/navigation';
import { authAPI } from '@/lib/api/auth.js';
import tokenStorage from '@/lib/tokenStorage.js';

// Create Auth Context
const AuthContext = createContext();

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();

  // Check if user is authenticated on mount
  useEffect(() => {
    checkAuth();
  }, []);

  const checkAuth = async () => {
    try {
      const authData = tokenStorage.getAuthData();
      if (authData.accessToken && authData.user && !tokenStorage.isTokenExpired(authData.accessToken)) {
        setUser(authData.user);
      } else {
        // Token expired or no user data, try to refresh
        if (authData.refreshToken && !tokenStorage.isTokenExpired(authData.refreshToken)) {
          try {
            const response = await authAPI.refreshToken();
            const { accessToken, refreshToken, user: userData } = response.data;
            
            tokenStorage.setAuthData({ accessToken, refreshToken, user: userData });
            setUser(userData);
          } catch (refreshError) {
            console.error('Token refresh failed:', refreshError);
            tokenStorage.clearAuthData();
          }
        } else {
          tokenStorage.clearAuthData();
        }
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      tokenStorage.clearAuthData();
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await authAPI.login(credentials);
      
      // Store tokens and user data
      const { accessToken, refreshToken, user: userData } = response.data;
      tokenStorage.setAuthData({ accessToken, refreshToken, user: userData });
      
      setUser(userData);
      
      // Redirect based on user role description
      if (userData.role?.description === 'Super Admin') {
        router.push('/listing');
      } else {
        router.push('/dashboard');
      }
      
      return { success: true, data: response.data };
    } catch (error) {
      let errorMessage = 'Login failed';
      
      if (error.response) {
        const { status, data } = error.response;
        
        switch (status) {
          case 400:
            errorMessage = data.message || 'Invalid credentials';
            break;
          case 401:
            errorMessage = data.message || 'Invalid email or password';
            break;
          case 403:
            errorMessage = data.message || 'Account is deactivated';
            break;
          case 429:
            errorMessage = data.message || 'Too many login attempts. Please try again later.';
            break;
          case 500:
            errorMessage = 'Server error. Please try again later.';
            break;
          default:
            errorMessage = data.message || 'Login failed';
        }
      } else if (error.request) {
        errorMessage = 'Network error. Please check your connection.';
      } else {
        errorMessage = error.message || 'Login failed';
      }
      
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const signup = async (userData) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await authAPI.signup(userData);
      
      // Store refresh and reset tokens from signup
      const { refreshToken, resetToken, newUser } = response.data;
      tokenStorage.setAuthData({ refreshToken, resetToken, user: newUser });
      
      return { success: true, data: response.data };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Signup failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear all auth data
      tokenStorage.clearAuthData();
      setUser(null);
      router.push('/login');
    }
  };

  const forgotPassword = async (email, url) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await authAPI.forgotPassword(email, url);
      
      return { success: true, data: response.data };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Failed to send reset email';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (token, newPassword, confirmPassword) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await authAPI.resetPassword(token, newPassword, confirmPassword);
      
      return { success: true, data: response.data };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Password reset failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const changePassword = async (oldPassword, newPassword, confirmPassword) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await authAPI.changePassword(oldPassword, newPassword, confirmPassword);
      
      return { success: true, data: response.data };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Password change failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (userId, userData) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await authAPI.updateProfile(userId, userData);
      
      // Update local user state and storage
      setUser(response.data);
      tokenStorage.setUserData(response.data);
      
      return { success: true, data: response.data };
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Profile update failed';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  const value = {
    user,
    loading,
    error,
    login,
    signup,
    logout,
    forgotPassword,
    resetPassword,
    changePassword,
    updateProfile,
    clearError,
    isAuthenticated: !!user,
    getUserRole: () => tokenStorage.getUserRole(),
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 