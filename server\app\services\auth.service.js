import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

import logger from '../config/logger.config.js';
import { LOG_MESSAGES } from '../utils/log_messages.utils.js'; // Corrected path for LOG_MESSAGES
import { CONSTANTS } from '../utils/constants.utils.js';
import { Role, User } from '../models/index.js';


const handleError = (logMessage, error) => {
  logger.error(logMessage, error);
  throw new Error(error.message || CONSTANTS.USER.INTERNAL_SERVER_ERROR);
};

export const createUser = async (userData) => {
  try {
    const user = await User.create(userData);
    logger.info(`${LOG_MESSAGES.USER.CREATED_SUCCESSFULLY}: ${user.id}`);
    return user;
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR.CREATING, error);
    throw new Error(error.message);
  }
};

export const checkUserExists = async (email) => {
  try {
    return await User.findOne({ 
      where: { email },
      include: [
        {
          model: Role,
          as: 'role',
          attributes: ['id', 'name', 'description']
        }
      ]
    });
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR.FETCHING_BY_EMAIL, error);
    throw new Error(error.message);
  }
};

export const checkUserExistsWithRole = async (email) => {
  try {
    return await User.findOne({ 
      where: { email },
      include: [
        {
          model: Role,
          as: 'role',
          attributes: ['id', 'name', 'description']
        }
      ]
    });
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR.FETCHING_BY_EMAIL, error);
    throw new Error(error.message);
  }
};


export const addLastLogin = async (userId) => {
  try {
    await User.update({ last_login: new Date() }, { where: { id: userId } });
    return await User.findByPk(userId);
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR.UPDATING_LAST_LOGIN, error);
    throw new Error(error.message);
  }
};

export const updateUserPassword = async (userId, newPassword) => {
  try {
    await User.update(
      {
        password: newPassword,
      },
      { where: { id: userId } }
    );
    return await User.findByPk(userId);
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.PASSWORD_RESET.ERROR_UPDATING_PASSWORD, error);
    throw new Error(error.message);
  }
};


export const findRolesByNames = async (roleNames) => {
  try {
    return await Role.findAll({ where: { name: roleNames } });
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR_FETCHING_ROLE_BY_NAME, error);
    throw new Error(error.message);
  }
};

export const updateUserPasswordAndToken = async (
  userId,
  resetPasswordToken,
  resetPasswordExpires
) => {
  try {
    // This function should now use the tokens table instead of user table
    // For now, we'll just update the password
    await User.update(
      {
        password: resetPasswordToken, // This should be the new password, not token
      },
      { where: { id: userId } }
    );
    return await User.findByPk(userId);
  } catch (error) {
    logger.error(LOG_MESSAGES.TOKEN.ERROR_UPDATING_PASSWORD, error);
    throw new Error(error.message);
  }
};


export const getUserByResetPasswordToken = async (resetPasswordToken) => {
  try {
    // This should now query the tokens table instead of user table
    // For now, return null as this needs to be updated to use tokens table
    return null;
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR_FETCHING_BY_RESET_PASSWORD_TOKEN, error);
    throw new Error(error.message);
  }
};

// Password & Token

export const validatePassword = async (enteredPassword, storedPassword) => {
  try {
    return await bcrypt.compare(enteredPassword, storedPassword);
  } catch (error) {
    handleError(LOG_MESSAGES.USER.ERROR_VALIDATING_PASSWORD, error);
  }
};

export const generateResetPasswordToken = (userId) => {
  try {
    const token = jwt.sign({ userId }, process.env.JWT_SECRET, {
      expiresIn: CONSTANTS.PASSWORD_RESET.TOKEN_EXPIRY,
    });

    logger.info(LOG_MESSAGES.TOKEN.RESET_PASSWORD_TOKEN_CREATED(userId));
    return token;
  } catch (error) {
    handleError(LOG_MESSAGES.USER.ERROR_GENERATING_RESET_PASSWORD_TOKEN, error);
  }
};

export const verifyResetPasswordToken = async (token) => {
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    logger.info(
      LOG_MESSAGES.TOKEN.RESET_PASSWORD_TOKEN_VERIFIED(decoded.userId)
    );
    return decoded;
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR_VERIFYING_RESET_PASSWORD_TOKEN, error);
    if (error.name === CONSTANTS.TokenExpiredError) {
      throw new Error(CONSTANTS.RESET_PASSWORD_TOKEN_EXPIRED);
    }
    throw new Error(CONSTANTS.INVALID_RESET_TOKEN);
  }
};

export const checkRoleExistsById = async (roleId) => {
  try {
    return await Role.findByPk(roleId);
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR_FETCHING_ROLE_BY_ID, error);
    throw new Error(error.message);
  }
};

export const findRoleByName = async (roleName) => {
  try {
    return await Role.findOne({ where: { name: roleName } });
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR_FETCHING_ROLE_BY_NAME, error);
    throw new Error(error.message);
  }
};

