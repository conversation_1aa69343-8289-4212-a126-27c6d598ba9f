import { Types } from 'mongoose';

const { ObjectId } = Types;

const userData = {
  _id: new ObjectId(),
  email: '<EMAIL>',
  password: 'Password@12345',
};

const updatedData = {
  email: '<EMAIL>',
};

const validUser = {
  email: '<EMAIL>',
  password: 'Password@123',
  _id: new ObjectId(),
};

const loginUser = {
  email: '<EMAIL>',
  password: 'Password@123',
};

const existingUser = {
  email: '<EMAIL>',
  password: 'Password@123',
  _id: '123',
};

const invalidUserCredentials = {
  email: '<EMAIL>',
  password: 'Invalidpassword',
};

const resetPasswordData = {
  token: 'fake-reset-token',
  newPassword: 'New@password123',
  confirmPassword: 'New@password123',
};

const mismatchedPasswordsData = {
  token: 'fake-reset-token',
  newPassword: 'New@password123',
  confirmPassword: 'Different@password123',
};

const expiredTokenData = {
  token: 'expired-reset-token',
  newPassword: 'New@password123',
  confirmPassword: 'New@password123',
  expires: Date.now() - 3600000,
};

const logData = {
  userId: new ObjectId(),
  action: 'login',
  successful: true,
  userAgent:
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  ipAddress: '***********',
};

const profileUser = {
  _id: new ObjectId(),
  email: '<EMAIL>',
};

const updatedProfileUser = {
  _id: profileUser._id,
  email: '<EMAIL>',
};

export {
  userData,
  validUser,
  loginUser,
  existingUser,
  updatedData,
  invalidUserCredentials,
  resetPasswordData,
  mismatchedPasswordsData,
  expiredTokenData,
  logData,
  profileUser,
  updatedProfileUser,
};
