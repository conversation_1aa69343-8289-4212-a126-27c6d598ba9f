import { Organization } from '../models/index.js';
import { dbOperations } from '../utils/db_operations.js';
import { createLogger } from '../utils/logger.utils.js';

const logger = createLogger('ORGANIZATION_SERVICE');

/**
 * Organization Service
 * Business logic for organization operations
 */
export const organizationService = {
  /**
   * Create a new organization
   * @param {Object} organizationData - Organization data
   * @returns {Promise<Object>} Created organization
   */
  async createOrganization(organizationData) {
    try {
      logger.info('Creating new organization');
      const organization = await dbOperations.create(
        Organization,
        organizationData
      );
      return organization;
    } catch (error) {
      logger.error('Error in createOrganization service:', error);
      throw error;
    }
  },

  /**
   * Get all organizations (excluding deleted ones)
   * @returns {Promise<Array>} Array of organizations
   */
  async getAllOrganizations() {
    try {
      logger.info('Fetching all organizations');
      const organizations = await dbOperations.findAll(Organization);
      return organizations;
    } catch (error) {
      logger.error('Error in getAllOrganizations service:', error);
      throw error;
    }
  },

  /**
   * Get organization by ID
   * @param {number} id - Organization ID
   * @returns {Promise<Object|null>} Organization or null
   */
  async getOrganizationById(id) {
    try {
      logger.info(`Fetching organization with ID: ${id}`);
      const organization = await dbOperations.findById(Organization, id);
      return organization;
    } catch (error) {
      logger.error(`Error in getOrganizationById service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Update organization by ID
   * @param {number} id - Organization ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated organization or null
   */
  async updateOrganization(id, updateData) {
    try {
      logger.info(`Updating organization with ID: ${id}`);
      const updatedOrganization = await dbOperations.update(
        Organization,
        id,
        updateData
      );
      return updatedOrganization;
    } catch (error) {
      logger.error(`Error in updateOrganization service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Soft delete organization by ID
   * @param {number} id - Organization ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteOrganization(id) {
    try {
      logger.info(`Soft deleting organization with ID: ${id}`);
      const success = await dbOperations.softDelete(Organization, id);
      return success;
    } catch (error) {
      logger.error(`Error in deleteOrganization service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Check if organization exists
   * @param {number} id - Organization ID
   * @returns {Promise<boolean>} Existence status
   */
  async organizationExists(id) {
    try {
      const exists = await dbOperations.exists(Organization, id);
      return exists;
    } catch (error) {
      logger.error(
        `Error checking organization existence for ID ${id}:`,
        error
      );
      throw error;
    }
  },
};

export default organizationService;
