import { Role, User } from '../models/index.js';
import { dbOperations } from '../utils/db_operations.js';
import { createLogger } from '../utils/logger.utils.js';
import logger from '../config/logger.config.js';
import { LOG_MESSAGES } from '../utils/log_messages.utils.js';

const serviceLogger = createLogger('USER_SERVICE');

/**
 * User Service
 * Business logic for user operations
 * Extended to follow new CRUD pattern while maintaining backward compatibility
 */
export const userService = {
  /**
   * Create a new user
   * @param {Object} userData - User data
   * @returns {Promise<Object>} Created user
   */
  async createUser(userData) {
    try {
      serviceLogger.info('Creating new user');
      const user = await dbOperations.create(User, userData);
      return user;
    } catch (error) {
      serviceLogger.error('Error in createUser service:', error);
      throw error;
    }
  },

  /**
   * Get all users (excluding deleted ones)
   * @returns {Promise<Array>} Array of users
   */
  async getAllUsers() {
    try {
      serviceLogger.info('Fetching all users');
      const users = await dbOperations.findAll(User, {
        include: [
          { association: 'Role', attributes: ['id', 'name'] },
          { association: 'Tenant', attributes: ['id', 'name'] },
          { association: 'Organization', attributes: ['id', 'name'] },
        ],
      });
      return users;
    } catch (error) {
      serviceLogger.error('Error in getAllUsers service:', error);
      throw error;
    }
  },

  /**
   * Get user by ID
   * @param {number} id - User ID
   * @returns {Promise<Object|null>} User or null
   */
  async getUserById(id) {
    try {
      serviceLogger.info(`Fetching user with ID: ${id}`);
      const user = await dbOperations.findById(User, id, {
        include: [
          { association: 'Role', attributes: ['id', 'name'] },
          { association: 'Tenant', attributes: ['id', 'name'] },
          { association: 'Organization', attributes: ['id', 'name'] },
        ],
      });
      return user;
    } catch (error) {
      serviceLogger.error(`Error in getUserById service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Update user by ID
   * @param {number} id - User ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated user or null
   */
  async updateUser(id, updateData) {
    try {
      serviceLogger.info(`Updating user with ID: ${id}`);
      const updatedUser = await dbOperations.update(User, id, updateData);
      return updatedUser;
    } catch (error) {
      serviceLogger.error(`Error in updateUser service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Soft delete user by ID
   * @param {number} id - User ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteUser(id) {
    try {
      serviceLogger.info(`Soft deleting user with ID: ${id}`);
      const success = await dbOperations.softDelete(User, id);
      return success;
    } catch (error) {
      serviceLogger.error(`Error in deleteUser service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Check if user exists
   * @param {number} id - User ID
   * @returns {Promise<boolean>} Existence status
   */
  async userExists(id) {
    try {
      const exists = await dbOperations.exists(User, id);
      return exists;
    } catch (error) {
      serviceLogger.error(`Error checking user existence for ID ${id}:`, error);
      throw error;
    }
  },
};

// Backward compatibility - keep existing functions
/**
 * Get all users from the database (legacy function)
 */
export const getAllUsers = async () => {
  try {
    logger.info(LOG_MESSAGES.USER.FETCHING_ALL);
    return await User.findAll();
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR.FETCHING_ALL, error);
    throw new Error(error.message);
  }
};

/**
 * Get a user by ID (legacy function)
 */
export const getUserById = async (id) => {
  try {
    logger.info(`${LOG_MESSAGES.USER.FETCHING_BY_ID}: ${id}`);
    return await dbOperations.findById(User, id, {
      include: [
        {
          model: Role,
          as: 'role',
          attributes: ['name'], // only fetch role name
        },
      ],
    });
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR.FETCHING_BY_ID, error);
    throw new Error(error.message);
  }
};

/**
 * Update a user by ID (legacy function)
 */
export const updateUser = async (id, userData) => {
  try {
    logger.info(`${LOG_MESSAGES.USER.UPDATING}: ${id}`);
    await User.update(userData, { where: { id } });
    return await User.findByPk(id);
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR.UPDATING, error);
    throw new Error(error.message);
  }
};

/**
 * Delete a user by ID (legacy function)
 */
export const deleteUser = async (id) => {
  try {
    logger.info(`${LOG_MESSAGES.USER.DELETING}: ${id}`);
    const user = await User.findByPk(id);
    if (!user) return null;
    await user.destroy();
    return user;
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR.DELETING, error);
    throw new Error(error.message);
  }
};

/**
 * Find a user by any condition (e.g. email)
 */
export const findUser = async (query) => {
  try {
    logger.info(LOG_MESSAGES.USER.FETCHING_BY_EMAIL);
    const key = Object.keys(query)[0];
    const value = query[key];
    const user = await User.findOne({ where: { [key]: value } });
    return user || null;
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR.FETCHING_BY_EMAIL, error);
    throw new Error(error.message);
  }
};

/**
 * Create a new user (legacy function)
 */
export const createUser = async (userData) => {
  try {
    const user = await User.create(userData);
    logger.info(`${LOG_MESSAGES.USER.CREATED_SUCCESSFULLY}: ${user.id}`);
    return user;
  } catch (error) {
    logger.error(LOG_MESSAGES.USER.ERROR.CREATING, error);
    throw new Error(error.message);
  }
};

export default userService;
