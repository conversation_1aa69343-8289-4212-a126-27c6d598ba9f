import 'dotenv/config';
import { app } from './app/server.js';
import { configureAuth0 } from './app/config/auth0.config.js';
import { initializeSSO } from './app/controllers/sso.controller.js';
import express from 'express';
import path from 'path';
import './app/models/index.js';
import { closeLocalConnection, connectLocalDB, sequelize } from './app/config/sequelize.js';

const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || 'development';
const SERVER_URL = process.env.SERVER_URL || `http://localhost:${PORT}`;

// Graceful shutdown handler
const gracefulShutdown = async (signal) => {
  console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
  try {
    await closeLocalConnection();
    console.log('✅ Database connection closed');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error);
    process.exit(1);
  }
};

// Process event handlers
const setupProcessHandlers = () => {
  const errorHandler = (error, event) => {
    console.error(`❌ ${event}:`, error);
    process.exit(1);
  };

  process.on('uncaughtException', (error) => errorHandler(error, 'Uncaught Exception'));
  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise);
    console.error('Reason:', reason);
    process.exit(1);
  });
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
};

// Static files setup
const setupStaticFiles = () => {
  const __dirname = path.dirname(
    new URL(import.meta.url).pathname.replace(/^\/+([A-Za-z]:)/, '$1')
  );
  app.use(express.static(path.join(__dirname, 'public')));
};

// Database initialization
const initializeDatabase = async () => {
  console.log('🔌 Initializing database connection...');
  
  try {
    // Test connection
    const connectionResult = await connectLocalDB();
    console.log('connectionResult', connectionResult);
    
    if (connectionResult) {
      console.log('✅ Database connection established successfully');
      
      // console.log(`📊 Connection Status:`, getConnectionStatus());
      
      // Authenticate and sync database
      // await sequelize.authenticate();
      // console.log('✅ Database authenticated successfully');
      await sequelize.sync();
      console.log('✅ Database tables synchronized');
      
      return true;
    } else {
      console.error('❌ Database connection failed:', connectionResult);
      return false;
    }
  } catch (error) {
    console.error('❌ Database initialization error:', error);
    return false;
  }
};

// Auth initialization
const initializeAuth = () => {
  console.log('🔐 Initializing authentication...');
  try {
    configureAuth0();
    initializeSSO();
    console.log('✅ Authentication initialized successfully');
  } catch (error) {
    console.error('❌ Authentication initialization error:', error);
    throw error;
  }
};

// Server startup
const startServer = async () => {
  try {
    console.log('🚀 Starting server...');
    console.log(`🌐 Environment: ${NODE_ENV}`);
    
    // Initialize database connection
    const dbConnected = await initializeDatabase();
    
    if (!dbConnected) {
      console.error('❌ Failed to connect to database. Server startup aborted.');
      process.exit(1);
    }

    // Initialize authentication
    initializeAuth();

    setupStaticFiles();

    const server = app.listen(PORT, () => {
      console.log(`✅ Server is running on port ${PORT}`);
      console.log(`🌐 Environment: ${NODE_ENV}`);
      console.log(`🔗 Health check available at: ${SERVER_URL}/health`);
      console.log(`📊 Connection status available at: ${SERVER_URL}/api/health/db`);
    });

    server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
      } else {
        console.error('❌ Server error:', error);
      }
      process.exit(1);
    });

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Initialize application
setupProcessHandlers();
startServer();
