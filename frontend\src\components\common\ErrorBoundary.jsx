"use client";

import React from "react";
import { <PERSON>ert<PERSON>riangle, RefreshCw, Home, Bug } from "lucide-react";
import { But<PERSON> } from "../ui/button";

/**
 * Error Boundary Component for catching JavaScript errors
 */
export default class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      eventId: null,
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Report error to monitoring service
    this.reportError(error, errorInfo);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  reportError = (error, errorInfo) => {
    // Here you would typically send the error to a monitoring service
    // like Sentry, LogRocket, or your own error tracking system
    
    const errorReport = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.props.userId,
    };

    // Example: Send to monitoring service
    if (this.props.reportError) {
      this.props.reportError(errorReport);
    }
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      eventId: null,
    });
  };

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback(
          this.state.error,
          this.state.errorInfo,
          this.handleRetry
        );
      }

      // Default error UI
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
          <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
            {/* Error Icon */}
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>

            {/* Error Title */}
            <h1 className="text-xl font-semibold text-gray-900 mb-2">
              {this.props.title || "Something went wrong"}
            </h1>

            {/* Error Message */}
            <p className="text-gray-600 mb-6">
              {this.props.message || 
                "We're sorry, but something unexpected happened. Please try again or contact support if the problem persists."}
            </p>

            {/* Error Details (Development only) */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="text-left mb-6 p-4 bg-gray-100 rounded-md">
                <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
                  <Bug className="inline h-4 w-4 mr-1" />
                  Error Details (Development)
                </summary>
                <div className="text-xs text-gray-600 font-mono">
                  <div className="mb-2">
                    <strong>Error:</strong> {this.state.error.message}
                  </div>
                  <div className="mb-2">
                    <strong>Stack:</strong>
                    <pre className="whitespace-pre-wrap mt-1 text-xs">
                      {this.state.error.stack}
                    </pre>
                  </div>
                  {this.state.errorInfo && (
                    <div>
                      <strong>Component Stack:</strong>
                      <pre className="whitespace-pre-wrap mt-1 text-xs">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {this.props.showRetry !== false && (
                <Button
                  onClick={this.handleRetry}
                  variant="default"
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </Button>
              )}
              
              {this.props.showReload && (
                <Button
                  onClick={this.handleReload}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Reload Page
                </Button>
              )}
              
              {this.props.showHome !== false && (
                <Button
                  onClick={this.handleGoHome}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Home className="h-4 w-4" />
                  Go Home
                </Button>
              )}
            </div>

            {/* Support Contact */}
            {this.props.supportEmail && (
              <div className="mt-6 pt-6 border-t border-gray-200">
                <p className="text-sm text-gray-500">
                  Need help?{" "}
                  <a
                    href={`mailto:${this.props.supportEmail}`}
                    className="text-blue-600 hover:text-blue-500"
                  >
                    Contact Support
                  </a>
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Higher-order component for wrapping components with error boundary
 * @param {React.Component} Component - Component to wrap
 * @param {Object} errorBoundaryProps - Props for error boundary
 * @returns {React.Component} Wrapped component
 */
export const withErrorBoundary = (Component, errorBoundaryProps = {}) => {
  const WrappedComponent = (props) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
};

/**
 * Specialized error boundaries for different contexts
 */

// Page-level error boundary
export const PageErrorBoundary = ({ children, ...props }) => (
  <ErrorBoundary
    title="Page Error"
    message="This page encountered an error. Please try refreshing or go back to the home page."
    showReload={true}
    showHome={true}
    {...props}
  >
    {children}
  </ErrorBoundary>
);

// Component-level error boundary
export const ComponentErrorBoundary = ({ children, componentName, ...props }) => (
  <ErrorBoundary
    title={`${componentName} Error`}
    message="This component encountered an error. Please try again."
    showRetry={true}
    showReload={false}
    showHome={false}
    {...props}
  >
    {children}
  </ErrorBoundary>
);

// API error boundary
export const APIErrorBoundary = ({ children, ...props }) => (
  <ErrorBoundary
    title="Connection Error"
    message="Unable to connect to the server. Please check your internet connection and try again."
    showRetry={true}
    showReload={true}
    showHome={false}
    {...props}
  >
    {children}
  </ErrorBoundary>
);

// Async component error boundary
export const AsyncErrorBoundary = ({ children, ...props }) => (
  <ErrorBoundary
    title="Loading Error"
    message="Failed to load this component. Please try again."
    showRetry={true}
    showReload={false}
    showHome={false}
    {...props}
  >
    {children}
  </ErrorBoundary>
);
