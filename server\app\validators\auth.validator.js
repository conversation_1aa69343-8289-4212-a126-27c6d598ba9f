import { body } from 'express-validator';
import { CONSTANTS } from '../utils/constants.utils.js';
import { PASSWORD_REGEX } from '../utils/pattern.utils.js';
import {
  PASSWORD,
  REQUEST_BODY,
} from '../utils/global.constants.js';

// Common validation rules
const commonValidations = {
  email: body(REQUEST_BODY.EMAIL)
    .notEmpty()
    .withMessage(CONSTANTS.VALIDATION.FIELD_REQUIRED)
    .isEmail()
    .withMessage(CONSTANTS.VALIDATION.INVALID_EMAIL),

  password: body(REQUEST_BODY.PASSWORD)
    .notEmpty()
    .withMessage(CONSTANTS.VALIDATION.FIELD_REQUIRED),
};

/**
 * Validation schema for creating a user
 */
export const signUpSchema = [
  body('name')
    .notEmpty()
    .withMessage('Name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),
  commonValidations.email,
  commonValidations.password
    .isLength({ min: PASSWORD.MIN_LENGTH, max: PASSWORD.MAX_LENGTH })
    .withMessage(CONSTANTS.VALIDATION.INVALID_PASSWORD_LENGTH)
    .matches(PASSWORD_REGEX)
    .withMessage(CONSTANTS.VALIDATION.INVALID_PASSWORD_FORMAT),
];

/**
 * Validation schema for logging in a user
 */
export const loginSchema = [
  commonValidations.email,
  commonValidations.password,
];

export const resetPasswordSchema = [
  body(REQUEST_BODY.TOKEN)
    .notEmpty()
    .withMessage(CONSTANTS.VALIDATION.FIELD_REQUIRED),
  body(REQUEST_BODY.NEW_PASSWORD)
    .notEmpty()
    .withMessage(CONSTANTS.VALIDATION.FIELD_REQUIRED),
  body(REQUEST_BODY.CONFIRM_PASSWORD)
    .notEmpty()
    .withMessage(CONSTANTS.VALIDATION.FIELD_REQUIRED),
];

export const forgotPasswordSchema = [
  body(REQUEST_BODY.EMAIL)
    .notEmpty()
    .withMessage(CONSTANTS.VALIDATION.FIELD_REQUIRED)
    .isEmail()
    .withMessage(CONSTANTS.VALIDATION.INVALID_EMAIL),
  body(REQUEST_BODY.URL)
    .notEmpty()
    .withMessage(CONSTANTS.VALIDATION.FIELD_REQUIRED)
    .isURL()
    .withMessage(CONSTANTS.VALIDATION.INVALID_URL),
];
