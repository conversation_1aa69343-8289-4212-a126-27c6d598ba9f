import { body } from 'express-validator';
import { CONSTANTS } from '../utils/constants.utils.js';
import { PASSWORD_REGEX } from '../utils/pattern.utils.js';
import { REQUEST_BODY } from '../utils/global.constants.js';

// Base validation rules that are common between create and update
const baseValidationRules = {
  name: body('name')
    .notEmpty()
    .withMessage('Name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('Name must be between 2 and 50 characters'),

  email: body(REQUEST_BODY.EMAIL)
    .notEmpty()
    .withMessage(CONSTANTS.VALIDATION.FIELD_REQUIRED)
    .isEmail()
    .withMessage(CONSTANTS.VALIDATION.INVALID_EMAIL),
};

/**
 * Validation schema for creating a user
 */
export const createUserSchema = [
  baseValidationRules.name,
  baseValidationRules.email,

  // Password validation only needed for create
  body(REQUEST_BODY.PASSWORD)
    .notEmpty()
    .withMessage(CONSTANTS.VALIDATION.FIELD_REQUIRED)
    .isLength({ min: 8, max: 15 })
    .withMessage(CONSTANTS.VALIDATION.INVALID_PASSWORD_LENGTH)
    .matches(PASSWORD_REGEX)
    .withMessage(CONSTANTS.VALIDATION.INVALID_PASSWORD_FORMAT),
];

/**
 * Validation schema for updating a user
 * <AUTHOR>
 */
export const updateUserSchema = [
  // Make base rules optional for updates
  baseValidationRules.name.optional(),
  baseValidationRules.email.optional(),

  // Role validation only needed for update
  body(REQUEST_BODY.ROLE_ID)
    .optional()
    .notEmpty()
    .withMessage(CONSTANTS.VALIDATION.FIELD_REQUIRED)
    .isMongoId()
    .withMessage(CONSTANTS.USER.INVALID_ROLE_ID),
];
