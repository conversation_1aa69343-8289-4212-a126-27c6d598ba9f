import express from 'express';
import userRoutes from './user.route.js';
import authRoutes from './auth.route.js';
import ssoRoutes from './sso.route.js';

// New CRUD Routes
import tenantRoutes from './tenant.routes.js';
import organizationRoutes from './organization.routes.js';
import userCrudRoutes from './user-crud.routes.js';
import roleRoutes from './role.routes.js';
import permissionRoutes from './permission.routes.js';

const router = express.Router();

// API Versions
const version1 = '/v1';

// Existing Routes
// User Routes (legacy)
router.use(`${version1}/users`, userRoutes);

// Auth Routes
router.use(`${version1}/auth`, authRoutes);

// SSO Routes
router.use(`${version1}/sso`, ssoRoutes);

// New CRUD Entity Routes
// Tenant Routes
router.use(`${version1}/tenant`, tenantRoutes);

// Organization Routes
router.use(`${version1}/organization`, organizationRoutes);

// User CRUD Routes (new pattern)
router.use(`${version1}/user`, userCrudRoutes);

// Role Routes
router.use(`${version1}/role`, roleRoutes);

// Permission Routes
router.use(`${version1}/permission`, permissionRoutes);

export default router;
