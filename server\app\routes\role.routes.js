import express from 'express';
import * as roleController from '../controllers/role.controller.js';
import { checkRole, verifyAccessToken } from '../middleware/auth.middleware.js';
import { CONSTANTS } from '../utils/constants.utils.js';

const router = express.Router();

/**
 * Role Routes
 * Prefix: /api/role
 * All routes require admin role
 */

/**
 * @route   GET /api/role
 * @desc    Get all roles (excluding deleted ones)
 * @access  Admin only
 */
router.get(
  '/',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN, CONSTANTS.ROLE.SUPER_ADMIN]),
  roleController.getAllRoles
);

/**
 * @route   GET /api/role/:id
 * @desc    Get role by ID
 * @access  Admin only
 */
router.get(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN, CONSTANTS.ROLE.SUPER_ADMIN]),
  roleController.getRoleById
);

/**
 * @route   POST /api/role
 * @desc    Create new role
 * @access  Admin only
 */
router.post(
  '/',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN, CONSTANTS.ROLE.SUPER_ADMIN]),
  roleController.createRole
);

/**
 * @route   PUT /api/role/:id
 * @desc    Update existing role
 * @access  Admin only
 */
router.put(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN, CONSTANTS.ROLE.SUPER_ADMIN]),
  roleController.updateRole
);

/**
 * @route   DELETE /api/role/:id
 * @desc    Soft delete role (set is_deleted = true)
 * @access  Admin only
 */
router.delete(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN, CONSTANTS.ROLE.SUPER_ADMIN]),
  roleController.deleteRole
);

export default router;
