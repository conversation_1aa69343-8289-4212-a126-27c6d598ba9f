'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Loader } from '@/components/ui/loading'

export default function HomePage() {
  const router = useRouter()
  const { isAuthenticated, user, loading } = useAuth()

  useEffect(() => {
    if (!loading) {
      if (!isAuthenticated) {
        router.push('/login')
      } else {
        // Redirect based on user role
        if (user?.role?.description === 'Super Admin') {
          router.push('/listing')
        } else {
          router.push('/dashboard')
        }
      }
    }
  }, [isAuthenticated, user, loading, router])

  // Show loader while checking authentication
  if (loading) {
    return <Loader text="Checking authentication..." />
  }

  // Show loader while redirecting
  return <Loader text="Redirecting..." />
}


