"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/toast";

/**
 * Custom hook for handling form operations with common patterns
 * @param {Object} options - Configuration options
 * @param {Object} options.initialValues - Initial form values
 * @param {Object} options.validationSchema - Zod validation schema
 * @param {Function} options.onSubmit - Submit handler function
 * @param {Function} options.onCancel - Cancel handler function
 * @param {Function} options.onSuccess - Success callback
 * @param {Function} options.onError - Error callback
 * @param {string} options.mode - Form mode: "create", "edit", "view"
 * @param {boolean} options.resetOnSuccess - Reset form after successful submission
 * @returns {Object} Form utilities and state
 */
export const useFormHandler = ({
  initialValues = {},
  validationSchema,
  onSubmit,
  onCancel,
  onSuccess,
  onError,
  mode = "create",
  resetOnSuccess = false,
} = {}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  const router = useRouter();
  const { addToast } = useToast();

  const isViewMode = mode === "view";
  const isEditMode = mode === "edit";
  const isCreateMode = mode === "create";

  const form = useForm({
    defaultValues: initialValues,
    resolver: validationSchema ? zodResolver(validationSchema) : undefined,
    mode: "onChange",
  });

  const handleSubmit = async (data) => {
    if (!onSubmit) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const result = await onSubmit(data, form);
      
      if (onSuccess) {
        onSuccess(result, data);
      }

      if (resetOnSuccess) {
        form.reset();
      }

      addToast("Operation completed successfully", "success");
    } catch (error) {
      const errorMessage = error.message || "An error occurred";
      setSubmitError(errorMessage);
      
      if (onError) {
        onError(error, data);
      }

      addToast(errorMessage, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      router.back();
    }
  };

  const handleReset = () => {
    form.reset(initialValues);
    setSubmitError(null);
  };

  const setFieldValue = (name, value) => {
    form.setValue(name, value, { shouldValidate: true });
  };

  const getFieldValue = (name) => {
    return form.getValues(name);
  };

  const setFieldError = (name, error) => {
    form.setError(name, { type: "manual", message: error });
  };

  const clearFieldError = (name) => {
    form.clearErrors(name);
  };

  const validateField = async (name) => {
    return await form.trigger(name);
  };

  const validateForm = async () => {
    return await form.trigger();
  };

  return {
    // Form instance
    form,

    // State
    isSubmitting,
    submitError,
    isViewMode,
    isEditMode,
    isCreateMode,

    // Handlers
    handleSubmit: form.handleSubmit(handleSubmit),
    handleCancel,
    handleReset,

    // Field utilities
    setFieldValue,
    getFieldValue,
    setFieldError,
    clearFieldError,
    validateField,
    validateForm,

    // Form state utilities
    isDirty: form.formState.isDirty,
    isValid: form.formState.isValid,
    errors: form.formState.errors,
    touchedFields: form.formState.touchedFields,
  };
};

/**
 * Hook for handling authentication forms (login, signup, etc.)
 */
export const useAuthForm = (options = {}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const formHandler = useFormHandler(options);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return {
    ...formHandler,
    showPassword,
    showConfirmPassword,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
  };
};

/**
 * Hook for handling search and filter forms
 */
export const useSearchForm = ({
  onSearch,
  onFilter,
  debounceMs = 300,
  initialSearchTerm = "",
  initialFilters = {},
} = {}) => {
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm);
  const [filters, setFilters] = useState(initialFilters);
  const [isSearching, setIsSearching] = useState(false);

  // Debounced search
  const [searchTimeout, setSearchTimeout] = useState(null);

  const handleSearch = (term) => {
    setSearchTerm(term);

    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(async () => {
      if (onSearch) {
        setIsSearching(true);
        try {
          await onSearch(term, filters);
        } finally {
          setIsSearching(false);
        }
      }
    }, debounceMs);

    setSearchTimeout(timeout);
  };

  const handleFilterChange = async (newFilters) => {
    setFilters(newFilters);
    
    if (onFilter) {
      setIsSearching(true);
      try {
        await onFilter(searchTerm, newFilters);
      } finally {
        setIsSearching(false);
      }
    }
  };

  const clearSearch = () => {
    setSearchTerm("");
    if (onSearch) {
      onSearch("", filters);
    }
  };

  const clearFilters = () => {
    setFilters(initialFilters);
    if (onFilter) {
      onFilter(searchTerm, initialFilters);
    }
  };

  const clearAll = () => {
    setSearchTerm("");
    setFilters(initialFilters);
    if (onSearch) {
      onSearch("", initialFilters);
    }
  };

  return {
    searchTerm,
    filters,
    isSearching,
    handleSearch,
    handleFilterChange,
    clearSearch,
    clearFilters,
    clearAll,
    setSearchTerm,
    setFilters,
  };
};
