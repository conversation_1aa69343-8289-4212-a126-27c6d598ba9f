// config/aws-config.js
import { Sequelize } from 'sequelize';
import dotenv from 'dotenv';

dotenv.config();

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASS,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: process.env.DB_DIALECT,
    dialectOptions: {
      ssl: {
        require: true,
        rejectUnauthorized: false,
      },
    },
    logging: false,
  }
);

// Connection status tracking
let connectionStatus = {
  connected: false,
  lastCheck: null,
  retryCount: 0,
  databaseType: process.env.DB_DIALECT || 'postgres',
  host: process.env.DB_HOST,
};

/**
 * Test database connection
 * @returns {Promise<Object>} Connection test result
 */
async function testConnection() {
  try {
    await sequelize.authenticate();
    
    const [results] = await sequelize.query('SELECT version() as version');
    const version = results[0]?.version || 'Unknown';
    
    // Update connection status
    connectionStatus.connected = true;
    connectionStatus.lastCheck = new Date();
    connectionStatus.retryCount = 0;
    
    return {
      success: true,
      message: 'Database connection successful',
      version: version,
      config: {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        database: process.env.DB_NAME,
        user: process.env.DB_USER,
        dialect: process.env.DB_DIALECT
      }
    };
    
  } catch (error) {
    // Update connection status
    connectionStatus.connected = false;
    connectionStatus.lastCheck = new Date();
    connectionStatus.retryCount += 1;
    
    return {
      success: false,
      error: error.message,
      config: {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        database: process.env.DB_NAME,
        user: process.env.DB_USER,
        dialect: process.env.DB_DIALECT
      }
    };
  }
}

/**
 * Get current connection status
 * @returns {Object} Connection status object
 */
function getConnectionStatus() {
  return { ...connectionStatus };
}

/**
 * Health check for the database
 * @returns {Promise<Object>} Health check result
 */
async function healthCheck() {
  try {
    const connectionTest = await testConnection();
    
    return {
      database: connectionTest.success ? 'connected' : 'disconnected',
      timestamp: new Date().toISOString(),
      status: connectionTest.success ? 'healthy' : 'unhealthy',
      connection: connectionTest
    };
  } catch (error) {
    return {
      database: 'error',
      timestamp: new Date().toISOString(),
      status: 'error',
      connection: {
        success: false,
        error: error.message
      }
    };
  }
}

/**
 * Close database connection
 * @returns {Promise<void>}
 */
async function closeConnection() {
  try {
    await sequelize.close();
    connectionStatus.connected = false;
    connectionStatus.lastCheck = new Date();
    console.log('Database connection closed successfully');
  } catch (error) {
    console.error('Error closing database connection:', error);
    throw error;
  }
}

// Sequelize CLI configuration
const config = {
  username: process.env.DB_USER,
  password: process.env.DB_PASS,
  database: process.env.DB_NAME,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  dialect: process.env.DB_DIALECT,
  dialectOptions: {
    ssl: {
      require: true,
      rejectUnauthorized: false,
    },
  },
  logging: false,
};

// Export for Sequelize CLI
export default {
  development: config,
  test: config,
  production: config,
};

// Export for application use
export { 
  sequelize, 
  testConnection, 
  getConnectionStatus, 
  healthCheck, 
  closeConnection,
  config
};
