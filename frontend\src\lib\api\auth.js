import api from "../axios.js";

// Auth API service functions
export const authAPI = {
  // Register a new user
  signup: async (userData) => {
    const response = await api.post("/auth/signup", userData);
    return response.data;
  },

  // Login user
  login: async (credentials) => {
    const response = await api.post("/auth/login", credentials);
    console.log(response.data)
    return response.data;
  },

  // Forgot password
  forgotPassword: async (email, url) => {
    const response = await api.post("/auth/forgot-password", { email, url });
    return response.data;
  },

  // Reset password
  resetPassword: async (token, newPassword, confirmPassword) => {
    const response = await api.post("/auth/reset-password", {
      token,
      newPassword,
      confirmPassword,
    });
    return response.data;
  },

  // Change password
  changePassword: async (oldPassword, newPassword, confirmPassword) => {
    const response = await api.put("/auth/change-password", {
      oldPassword,
      newPassword,
      confirmPassword,
    });
    return response.data;
  },

  // Get user profile
  getProfile: async () => {
    const response = await api.get("/auth/me");
    return response.data;
  },

  // Update user profile
  updateProfile: async (userId, userData) => {
    const response = await api.put(`/auth/profile/${userId}`, userData);
    return response.data;
  },

  // Logout user
  logout: async () => {
    const response = await api.post("/auth/logout");
    return response.data;
  },

  // Refresh token
  refreshToken: async () => {
    const response = await api.post("/auth/refresh-token");
    return response.data;
  },
};
