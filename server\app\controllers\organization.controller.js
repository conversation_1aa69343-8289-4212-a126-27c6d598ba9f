import { organizationService } from '../services/organization.service.js';
import { createLogger } from '../utils/logger.utils.js';
import { 
  STATUS_CODE_SUCCESS,
  STATUS_CODE_CREATED,
  STATUS_CODE_NOT_FOUND,
  STATUS_CODE_INTERNAL_SERVER_STATUS,
  STATUS_CODE_BAD_REQUEST
} from '../utils/status_code.utils.js';

const logger = createLogger('ORGANIZATION_CONTROLLER');

/**
 * Create a new organization
 */
export const createOrganization = async (req, res) => {
  try {
    logger.info('Creating new organization');
    const organizationData = req.body;
    
    const organization = await organizationService.createOrganization(organizationData);
    
    return res.status(STATUS_CODE_CREATED).json({
      success: true,
      message: 'Organization created successfully',
      data: organization
    });
  } catch (error) {
    logger.error('Error in createOrganization controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get all organizations
 */
export const getAllOrganizations = async (req, res) => {
  try {
    logger.info('Fetching all organizations');
    const organizations = await organizationService.getAllOrganizations();
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Organizations fetched successfully',
      data: organizations,
      count: organizations.length
    });
  } catch (error) {
    logger.error('Error in getAllOrganizations controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get organization by ID
 */
export const getOrganizationById = async (req, res) => {
  try {
    const { id } = req.params;
    logger.info(`Fetching organization with ID: ${id}`);
    
    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid organization ID'
      });
    }
    
    const organization = await organizationService.getOrganizationById(parseInt(id));
    
    if (!organization) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'Organization not found'
      });
    }
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Organization fetched successfully',
      data: organization
    });
  } catch (error) {
    logger.error('Error in getOrganizationById controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Update organization by ID
 */
export const updateOrganization = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    logger.info(`Updating organization with ID: ${id}`);
    
    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid organization ID'
      });
    }
    
    const updatedOrganization = await organizationService.updateOrganization(parseInt(id), updateData);
    
    if (!updatedOrganization) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'Organization not found or already deleted'
      });
    }
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Organization updated successfully',
      data: updatedOrganization
    });
  } catch (error) {
    logger.error('Error in updateOrganization controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Soft delete organization by ID
 */
export const deleteOrganization = async (req, res) => {
  try {
    const { id } = req.params;
    logger.info(`Soft deleting organization with ID: ${id}`);
    
    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid organization ID'
      });
    }
    
    const success = await organizationService.deleteOrganization(parseInt(id));
    
    if (!success) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'Organization not found or already deleted'
      });
    }
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Organization deleted successfully'
    });
  } catch (error) {
    logger.error('Error in deleteOrganization controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
