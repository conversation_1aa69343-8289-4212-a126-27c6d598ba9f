import bcrypt from 'bcryptjs';
import logger from '../config/logger.config.js';
import * as authService from '../services/auth.service.js';
import * as userService from '../services/user.service.js';
import * as tokenService from '../services/token.service.js';
import * as emailService from '../utils/email.utils.js';
import { CONSTANTS } from '../utils/constants.utils.js';
import * as status from '../utils/status_code.utils.js';
import { LOG_MESSAGES } from '../utils/log_messages.utils.js';
import { errorResponse, successResponse } from '../utils/response.util.js';
import { verifyToken } from '../utils/jwt.utils.js';
import { checkValidation, handleError } from '../utils/validation.utils.js';
import { setSecureCookie, clearCookie } from '../utils/cookie.utils.js';
import { TIME, TOKEN_TYPES } from '../utils/global.constants.js';

// Check if user exists by email or phone
const getUserIfExists = async (email, phone) =>
  await authService.checkUserExists(email, phone);

/**
 * @param {*} req
 * @param {*} res
 * @returns {JSON}
 * @description Register a new user
 * @route /api/auth/signup
 * @method POST
 */
export const signUp = async (req, res) => {
  if (checkValidation(req, res)) return;

  try {
    logger.info(LOG_MESSAGES.AUTH.SIGNING_UP);

    const { role_id: role, name, phone_number, ...userData } = req.body;

    userData.role_id = role;
    // Handle required and optional fields
    userData.name = name; // Required field
    userData.phone_number = phone_number || null; // Optional field

    const roleDoc = await authService.checkRoleExistsById(role);

    if (!roleDoc) {
      logger.warn(LOG_MESSAGES.ROLE.INVALID_ROLE);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.ROLE.INVALID_PROVIDED));
    }

    if (await getUserIfExists(userData.email)) {
      logger.warn(LOG_MESSAGES.USER.EXISTS_EMAIL);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.USER.ALREADY_EXISTS_ERROR));
    }

    userData.password = await bcrypt.hash(userData.password, 8);
    const newUser = await authService.createUser(userData);

    // Generate refresh and reset tokens for signup
    const refreshToken = await tokenService.createRefreshToken(newUser);
    const resetToken = await tokenService.createResetToken(newUser);

    // Email sending temporarily disabled
    await emailService.sendWelcomeEmail(userData.email, userData.name || 'User');

    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(CONSTANTS.AUTH.SIGNUP_SUCCESSFULLY, { 
        newUser,
        refreshToken,
        resetToken 
      }));
  } catch (error) {
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.AUTH.ERROR_SIGN_UP
    );

    res.status(errorStatus).json(response);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns {JSON}
 * @description Login a user
 * @route /api/auth/login
 * @method POST
 */
export const login = async (req, res) => {
  if (checkValidation(req, res)) return;

  const { email, password } = req.body;

  try {
    logger.info(LOG_MESSAGES.AUTH.LOGGING_IN);
    
    // Check if user exists with role information
    const user = await authService.checkUserExistsWithRole(email);
    
    if (!user) {
      logger.warn(LOG_MESSAGES.AUTH.INVALID_CREDENTIALS);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse('Invalid email or password'));
    }

    // Check if user is active
    if (!user.is_active) {
      logger.warn(LOG_MESSAGES.AUTH.ACCOUNT_INACTIVE);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse('Account is deactivated. Please contact support.'));
    }

    // Validate password
    const isValidPassword = await authService.validatePassword(password, user.password);
    if (!isValidPassword) {
      logger.warn(LOG_MESSAGES.AUTH.INVALID_CREDENTIALS);
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse('Invalid email or password'));
    }

    // Generate tokens
    const accessToken = await tokenService.createAccessToken(user);
    const refreshToken = await tokenService.createRefreshToken(user);

    // Set secure cookies
    setSecureCookie(res, 'accessToken', accessToken, TIME.ACCESS_TOKEN_EXPIRY);
    setSecureCookie(res, 'refreshToken', refreshToken, TIME.REFRESH_TOKEN_EXPIRY);

    // Remove password from response and format user data with role
    const { password: _, ...userWithoutPassword } = user.toJSON();
    
    // Format the response to include role information
    const userResponse = {
      ...userWithoutPassword,
      role: {
        id: user.role?.id,
        name: user.role?.name,
        description: user.role?.description
      }
    };

    logger.info(LOG_MESSAGES.AUTH.LOGIN_SUCCESSFUL);
    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(CONSTANTS.AUTH.LOGIN_SUCCESSFULLY, {
        user: userResponse,
        accessToken,
        refreshToken
      }));
  } catch (error) {
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.AUTH.ERROR_LOGIN
    );

    res.status(errorStatus).json(response);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns {JSON}
 * @description Request password reset
 * @route /api/auth/forgot-password
 * @method POST
 */
export const forgotPassword = async (req, res) => {
  if (checkValidation(req, res)) return;

  try {
    logger.info(LOG_MESSAGES.PASSWORD_RESET.EMAIL_SENT);
    const { email, url } = req.body;

    const user = await authService.checkUserExists(email);
    if (!user)
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.USER.DOES_NOT_EXIST));
    
    // Generate reset token using token service
    const resetToken = await tokenService.createResetToken(user);

    await emailService.sendForgotPasswordEmail(email, url, resetToken);

    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(CONSTANTS.PASSWORD_RESET.EMAIL_SENT));
  } catch (error) {
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.PASSWORD_RESET.ERROR_EMAIL_SENT
    );
    res.status(errorStatus).json(response);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns {JSON}
 * @description Change user password
 * @route /api/auth/change-password
 * @method PUT
 */
export const changePassword = async (req, res) => {
  if (checkValidation(req, res)) return;

  try {
    logger.info(LOG_MESSAGES.PASSWORD_RESET.CHANGE_PASSWORD);
    const { oldPassword, newPassword, confirmPassword } = req.body;

    const user = await authService.checkUserExists(req.user.email);
    if (!user)
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.USER.DOES_NOT_EXIST));

    if (!(await authService.validatePassword(oldPassword, user.password)))
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.PASSWORD_RESET.OLD_PASSWORD_INCORRECT));

    if (newPassword !== confirmPassword)
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.PASSWORD_RESET.CONFIRM_MISMATCH));

    const hashedPassword = await bcrypt.hash(newPassword, 8);
    await authService.updateUserPassword(user.id, hashedPassword);

    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(CONSTANTS.PASSWORD_RESET.SUCCESS));
  } catch (error) {
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.PASSWORD_RESET.ERROR_CHANGE_PASSWORD
    );
    res.status(errorStatus).json(response);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns {JSON}
 * @description Reset user password
 * @route /api/auth/reset-password
 * @method POST
 */
export const resetPassword = async (req, res) => {
  if (checkValidation(req, res)) return;

  try {
    logger.info(LOG_MESSAGES.PASSWORD_RESET.RESET_SUCCESS);
    const { token, newPassword, confirmPassword } = req.body;

    if (newPassword !== confirmPassword)
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.PASSWORD_RESET.CONFIRM_MISMATCH));

    // Validate reset token from database
    const tokenRecord = await tokenService.validateToken(token, 'reset');
    if (!tokenRecord) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.PASSWORD_RESET.INVALID_TOKEN));
    }

    const decoded = verifyToken(token, process.env.JWT_RESET_SECRET || process.env.JWT_ACCESS_SECRET);
    const user = await userService.getUserById(decoded.id);
    if (!user) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.USER.NOT_FOUND));
    }

    const hashedPassword = await bcrypt.hash(newPassword, 8);
    await authService.updateUserPassword(user.id, hashedPassword);

    // Revoke the reset token after successful password reset
    await tokenService.revokeToken(token, 'reset');

    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(CONSTANTS.PASSWORD_RESET.SUCCESS));
  } catch (error) {
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.PASSWORD_RESET.ERROR_RESET_FAILED
    );
    res.status(errorStatus).json(response);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns {JSON}
 * @description Logout user
 * @route /api/auth/logout
 * @method POST
 */
export const logout = async (req, res) => {
  try {
    // Revoke all tokens for the user
    await tokenService.revokeAllUserTokens(req.user.id);
    
    clearCookie(res, TOKEN_TYPES.ACCESS.KEY);
    clearCookie(res, TOKEN_TYPES.REFRESH.KEY);

    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(CONSTANTS.AUTH.LOGOUT_SUCCESS));
  } catch (error) {
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.AUTH.ERROR_LOGGING_OUT
    );
    res.status(errorStatus).json(response);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns {JSON}
 * @description Refresh access and refresh tokens
 * @route /api/auth/refresh-token
 * @method POST
 */
export const refreshToken = async (req, res) => {
  try {
    const token = req.cookies?.refreshToken;
    logger.info(LOG_MESSAGES.AUTH.REFRESHING_TOKEN);
    if (!token)
      return res
        .status(status.STATUS_CODE_UNAUTHORIZED)
        .json(errorResponse(CONSTANTS.REFRESH_TOKEN.REQUIRED));

    // Validate token from database
    const tokenRecord = await tokenService.validateToken(token, 'refresh');
    if (!tokenRecord) {
      return res
        .status(status.STATUS_CODE_UNAUTHORIZED)
        .json(errorResponse(CONSTANTS.REFRESH_TOKEN.INVALID));
    }

    const decoded = verifyToken(token, process.env.JWT_REFRESH_SECRET);
    const user = await userService.getUserById(decoded.id);
    if (!user)
      return res
        .status(status.STATUS_CODE_UNAUTHORIZED)
        .json(errorResponse(CONSTANTS.USER.NOT_FOUND));

    console.log('User:', user);
    // Revoke old refresh token
    await tokenService.revokeToken(token, 'refresh');

    // Generate new tokens
    const accessToken = await tokenService.createAccessToken(user);
    const refreshToken = await tokenService.createRefreshToken(user);

    setSecureCookie(res, TOKEN_TYPES.ACCESS.KEY, accessToken);
    setSecureCookie(res, TOKEN_TYPES.REFRESH.KEY, refreshToken);

    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(CONSTANTS.REFRESH_TOKEN.SUCCESS, {
        accessToken,
        refreshToken,
      }));
  } catch (error) {
    if (error.name === CONSTANTS.ERRORS.TOKEN_EXPIRED) {
      return res
        .status(status.STATUS_CODE_UNAUTHORIZED)
        .json(errorResponse(CONSTANTS.REFRESH_TOKEN.EXPIRED));
    }
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.AUTH.REFRESH_ERROR
    );
    res.status(errorStatus).json(response);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns {JSON}
 * @description Get user profile
 * @route /api/auth/profile
 * @method GET
 */
export const getProfile = async (req, res) => {
  try {
    const user = await userService.getUserById(req.user.id);
    if (!user) {
      return res
        .status(status.STATUS_CODE_NOT_FOUND)
        .json(errorResponse(CONSTANTS.USER.NOT_FOUND));
    }

    // Get user with role information
    const userWithRole = await authService.checkUserExistsWithRole(user.email);
    
    // Format the response to include role information
    const { password: _, ...userWithoutPassword } = userWithRole.toJSON();
    const userResponse = {
      ...userWithoutPassword,
      role: {
        id: userWithRole.role?.id,
        name: userWithRole.role?.name,
        description: userWithRole.role?.description
      }
    };

    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(CONSTANTS.USER.USER_INFO_RETRIEVED_SUCCESSFULLY, userResponse)
      );
  } catch (error) {
    logger.error('Error getting user profile:', error);
    res
      .status(status.STATUS_CODE_INTERNAL_SERVER_STATUS)
      .json(errorResponse(CONSTANTS.USER.INTERNAL_SERVER_ERROR, error.message));
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns {JSON}
 * @description Update user profile
 * @route /api/auth/update-profile/:id
 * @method PUT
 */
export const updateProfile = async (req, res) => {
  try {
    const { id } = req.params;
    const userData = req.body;

    const updatedUser = await userService.updateUser(id, userData);

    if (!updatedUser) {
      return res
        .status(status.STATUS_CODE_NOT_FOUND)
        .json(errorResponse(CONSTANTS.USER.NOT_FOUND));
    }

    res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(CONSTANTS.USER.UPDATED_SUCCESSFULLY, updatedUser));
  } catch (error) {
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.USER.ERROR_UPDATING
    );
    res.status(errorStatus).json(response);
  }
};
