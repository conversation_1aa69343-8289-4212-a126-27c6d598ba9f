// test-connection.js
import { testConnection } from './app/config/aws-config.js';

async function runConnectionTest() {
  try {
    const result = await testConnection();
    
    if (result.success) {
      console.log('Database connection successful');
      console.log(`Version: ${result.version}`);
    } else {
      console.log('Database connection failed');
      console.log(`Error: ${result.error}`);
    }
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
  
  process.exit(0);
}

runConnectionTest(); 