import { tenantService } from '../services/tenant.service.js';
import { createLogger } from '../utils/logger.utils.js';
import { 
  STATUS_CODE_SUCCESS,
  STATUS_CODE_CREATED,
  STATUS_CODE_NOT_FOUND,
  STATUS_CODE_INTERNAL_SERVER_STATUS,
  STATUS_CODE_BAD_REQUEST
} from '../utils/status_code.utils.js';

const logger = createLogger('TENANT_CONTROLLER');

/**
 * Create a new tenant
 */
export const createTenant = async (req, res) => {
  try {
    logger.info('Creating new tenant');
    const tenantData = req.body;
    
    const tenant = await tenantService.createTenant(tenantData);
    
    return res.status(STATUS_CODE_CREATED).json({
      success: true,
      message: 'Tenant created successfully',
      data: tenant
    });
  } catch (error) {
    logger.error('Error in createTenant controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get all tenants
 */
export const getAllTenants = async (req, res) => {
  try {
    logger.info('Fetching all tenants');
    const tenants = await tenantService.getAllTenants();
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Tenants fetched successfully',
      data: tenants,
      count: tenants.length
    });
  } catch (error) {
    logger.error('Error in getAllTenants controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get tenant by ID
 */
export const getTenantById = async (req, res) => {
  try {
    const { id } = req.params;
    console.log('id:', id);
    logger.info(`Fetching tenant with ID: ${id}`);
    
    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid tenant ID'
      });
    }
    
    const tenant = await tenantService.getTenantById(parseInt(id));
    
    if (!tenant) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'Tenant not found'
      });
    }
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Tenant fetched successfully',
      data: tenant
    });
  } catch (error) {
    logger.error('Error in getTenantById controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Update tenant by ID
 */
export const updateTenant = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    logger.info(`Updating tenant with ID: ${id}`);
    
    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid tenant ID'
      });
    }
    
    const updatedTenant = await tenantService.updateTenant(parseInt(id), updateData);
    
    if (!updatedTenant) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'Tenant not found or already deleted'
      });
    }
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Tenant updated successfully',
      data: updatedTenant
    });
  } catch (error) {
    logger.error('Error in updateTenant controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Soft delete tenant by ID
 */
export const deleteTenant = async (req, res) => {
  try {
    const { id } = req.params;
    logger.info(`Soft deleting tenant with ID: ${id}`);
    
    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid tenant ID'
      });
    }
    
    const success = await tenantService.deleteTenant(parseInt(id));
    
    if (!success) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'Tenant not found or already deleted'
      });
    }
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Tenant deleted successfully'
    });
  } catch (error) {
    logger.error('Error in deleteTenant controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
