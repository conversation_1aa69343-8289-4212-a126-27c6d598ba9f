import express from 'express';
import { 
  createUserNew, 
  getAllUsersNew, 
  getUserByIdNew, 
  updateUserNew, 
  deleteUserNew 
} from '../controllers/user.controller.js';
import { checkRole, verifyAccessToken } from '../middleware/auth.middleware.js';
import { CONSTANTS } from '../utils/constants.utils.js';

const router = express.Router();

/**
 * User CRUD Routes (New Pattern)
 * Prefix: /api/user
 * All routes require admin role
 */

/**
 * @route   GET /api/user
 * @desc    Get all users (excluding deleted ones)
 * @access  Admin only
 */
router.get(
  '/',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  getAllUsersNew
);

/**
 * @route   GET /api/user/:id
 * @desc    Get user by ID
 * @access  Admin only
 */
router.get(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  getUserByIdNew
);

/**
 * @route   POST /api/user
 * @desc    Create new user
 * @access  Admin only
 */
router.post(
  '/',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  
);

/**
 * @route   PUT /api/user/:id
 * @desc    Update existing user
 * @access  Admin only
 */
router.put(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  updateUserNew
);

/**
 * @route   DELETE /api/user/:id
 * @desc    Soft delete user (set is_deleted = true)
 * @access  Admin only
 */
router.delete(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  deleteUserNew
);

export default router;
