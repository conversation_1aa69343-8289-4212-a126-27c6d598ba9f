import api from "../axios.js";
import { ROUTES } from "@/utils/const/routes";

/**
 * Base API service class with common CRUD operations
 */
export class BaseAPIService {
  constructor(endpoint) {
    this.endpoint = endpoint;
  }

  /**
   * Get all items with optional query parameters
   * @param {Object} params - Query parameters
   * @returns {Promise} API response
   */
  async getAll(params = {}) {
    const response = await api.get(this.endpoint, { params });
    return response.data;
  }

  /**
   * Get paginated items
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @param {Object} params - Additional query parameters
   * @returns {Promise} API response
   */
  async getPaginated(page = 1, limit = 10, params = {}) {
    const response = await api.get(this.endpoint, {
      params: { page, limit, ...params },
    });
    return response.data;
  }

  /**
   * Get item by ID
   * @param {string|number} id - Item ID
   * @returns {Promise} API response
   */
  async getById(id) {
    const response = await api.get(`${this.endpoint}/${id}`);
    return response.data;
  }

  /**
   * Create new item
   * @param {Object} data - Item data
   * @returns {Promise} API response
   */
  async create(data) {
    const response = await api.post(this.endpoint, data);
    return response.data;
  }

  /**
   * Update item by ID
   * @param {string|number} id - Item ID
   * @param {Object} data - Updated data
   * @returns {Promise} API response
   */
  async update(id, data) {
    const response = await api.put(`${this.endpoint}/${id}`, data);
    return response.data;
  }

  /**
   * Partially update item by ID
   * @param {string|number} id - Item ID
   * @param {Object} data - Partial data
   * @returns {Promise} API response
   */
  async patch(id, data) {
    const response = await api.patch(`${this.endpoint}/${id}`, data);
    return response.data;
  }

  /**
   * Delete item by ID
   * @param {string|number} id - Item ID
   * @returns {Promise} API response
   */
  async delete(id) {
    const response = await api.delete(`${this.endpoint}/${id}`);
    return response.data;
  }

  /**
   * Bulk delete items
   * @param {Array} ids - Array of item IDs
   * @returns {Promise} API response
   */
  async bulkDelete(ids) {
    const response = await api.delete(`${this.endpoint}/bulk`, {
      data: { ids },
    });
    return response.data;
  }

  /**
   * Search items
   * @param {string} query - Search query
   * @param {Object} params - Additional parameters
   * @returns {Promise} API response
   */
  async search(query, params = {}) {
    const response = await api.get(`${this.endpoint}/search`, {
      params: { q: query, ...params },
    });
    return response.data;
  }

  /**
   * Get item count
   * @param {Object} params - Filter parameters
   * @returns {Promise} API response
   */
  async count(params = {}) {
    const response = await api.get(`${this.endpoint}/count`, { params });
    return response.data;
  }

  /**
   * Export items
   * @param {Object} params - Export parameters
   * @param {string} format - Export format (csv, xlsx, pdf)
   * @returns {Promise} API response
   */
  async export(params = {}, format = "csv") {
    const response = await api.get(`${this.endpoint}/export`, {
      params: { format, ...params },
      responseType: "blob",
    });
    return response;
  }

  /**
   * Import items
   * @param {File} file - File to import
   * @param {Object} options - Import options
   * @returns {Promise} API response
   */
  async import(file, options = {}) {
    const formData = new FormData();
    formData.append("file", file);
    
    Object.keys(options).forEach(key => {
      formData.append(key, options[key]);
    });

    const response = await api.post(`${this.endpoint}/import`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  }
}

/**
 * API response wrapper with standardized error handling
 */
export class APIResponse {
  constructor(data, status, message = "") {
    this.data = data;
    this.status = status;
    this.message = message;
    this.success = status >= 200 && status < 300;
  }

  static success(data, message = "Success") {
    return new APIResponse(data, 200, message);
  }

  static error(message = "Error", status = 500) {
    return new APIResponse(null, status, message);
  }
}

/**
 * API error handler utility
 */
export const handleAPIError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    return {
      status,
      message: data?.message || data?.error || "Server error",
      errors: data?.errors || [],
    };
  } else if (error.request) {
    // Request was made but no response received
    return {
      status: 0,
      message: "Network error - please check your connection",
      errors: [],
    };
  } else {
    // Something else happened
    return {
      status: 0,
      message: error.message || "An unexpected error occurred",
      errors: [],
    };
  }
};

/**
 * Create API service instance for a specific endpoint
 * @param {string} endpoint - API endpoint
 * @returns {BaseAPIService} API service instance
 */
export const createAPIService = (endpoint) => {
  return new BaseAPIService(endpoint);
};

/**
 * API request wrapper with error handling
 * @param {Function} apiCall - API call function
 * @param {Object} options - Options
 * @returns {Promise} Wrapped API response
 */
export const withErrorHandling = async (apiCall, options = {}) => {
  try {
    const result = await apiCall();
    return APIResponse.success(result, options.successMessage);
  } catch (error) {
    const errorInfo = handleAPIError(error);
    
    if (options.throwError) {
      throw error;
    }
    
    return APIResponse.error(errorInfo.message, errorInfo.status);
  }
};

/**
 * Retry API call with exponential backoff
 * @param {Function} apiCall - API call function
 * @param {number} maxRetries - Maximum retry attempts
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise} API response
 */
export const withRetry = async (apiCall, maxRetries = 3, baseDelay = 1000) => {
  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        break;
      }

      // Don't retry on client errors (4xx)
      if (error.response?.status >= 400 && error.response?.status < 500) {
        break;
      }

      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
};
