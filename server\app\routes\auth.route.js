import express from 'express';
import * as authController from '../controllers/auth.controller.js';
import { verifyAccessToken } from '../middleware/auth.middleware.js';
import { validate } from '../middleware/validate.middleware.js';
import { authRateLimiter, signupRateLimiter } from '../middleware/ratelimit.middleware.js';
import { 
  signUpSchema, 
  loginSchema, 
  forgotPasswordSchema, 
  resetPasswordSchema 
} from '../validators/auth.validator.js';

const router = express.Router();

// Register a new user
router.post('/signup', signupRateLimiter(), validate(signUpSchema), authController.signUp);

// Login user
router.post('/login', authRateLimiter(), validate(loginSchema), authController.login);

// Forgot password
router.post('/forgot-password', authRateLimiter(), validate(forgotPasswordSchema), authController.forgotPassword);

// Reset password
router.post('/reset-password', validate(resetPasswordSchema), authController.resetPassword);

// Get User Profile
router.get('/me', verifyAccessToken, authController.getProfile);

// Change Password
router.put('/change-password', verifyAccessToken, authController.changePassword);

// Update User Profile
router.put('/profile/:id', verifyAccessToken, authController.updateProfile);

// Add logout route
router.post('/logout', verifyAccessToken, authController.logout);

// Add refresh token route
router.post('/refresh-token', authController.refreshToken);

export default router;
