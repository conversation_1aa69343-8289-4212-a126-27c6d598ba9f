import { Permission } from '../models/index.js';
import { dbOperations } from '../utils/db_operations.js';
import { createLogger } from '../utils/logger.utils.js';

const logger = createLogger('PERMISSION_SERVICE');

/**
 * Permission Service
 * Business logic for permission operations
 */
export const permissionService = {
  /**
   * Create a new permission
   * @param {Object} permissionData - Permission data
   * @returns {Promise<Object>} Created permission
   */
  async createPermission(permissionData) {
    try {
      logger.info('Creating new permission');
      const permission = await dbOperations.create(Permission, permissionData);
      return permission;
    } catch (error) {
      logger.error('Error in createPermission service:', error);
      throw error;
    }
  },

  /**
   * Get all permissions (excluding deleted ones)
   * @returns {Promise<Array>} Array of permissions
   */
  async getAllPermissions() {
    try {
      logger.info('Fetching all permissions');
      const permissions = await dbOperations.findAll(Permission, {
        where: { is_deleted: false },
        order: [['created_at', 'DESC']]
      });
      return permissions;
    } catch (error) {
      logger.error('Error in getAllPermissions service:', error);
      throw error;
    }
  },

  /**
   * Get permission by ID
   * @param {number} id - Permission ID
   * @returns {Promise<Object|null>} Permission or null
   */
  async getPermissionById(id) {
    try {
      logger.info(`Fetching permission with ID: ${id}`);
      const permission = await dbOperations.findById(Permission, id, {
        where: { is_deleted: false }
      });
      return permission;
    } catch (error) {
      logger.error(`Error in getPermissionById service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Update permission by ID
   * @param {number} id - Permission ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated permission or null
   */
  async updatePermission(id, updateData) {
    try {
      logger.info(`Updating permission with ID: ${id}`);
      const updatedPermission = await dbOperations.update(Permission, id, updateData);
      return updatedPermission;
    } catch (error) {
      logger.error(`Error in updatePermission service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Soft delete permission by ID
   * @param {number} id - Permission ID
   * @returns {Promise<boolean>} Success status
   */
  async deletePermission(id) {
    try {
      logger.info(`Soft deleting permission with ID: ${id}`);
      const success = await dbOperations.softDelete(Permission, id);
      return success;
    } catch (error) {
      logger.error(`Error in deletePermission service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Check if permission exists
   * @param {number} id - Permission ID
   * @returns {Promise<boolean>} Existence status
   */
  async permissionExists(id) {
    try {
      const exists = await dbOperations.exists(Permission, id);
      return exists;
    } catch (error) {
      logger.error(`Error checking permission existence for ID ${id}:`, error);
      throw error;
    }
  }
};

export default permissionService;
