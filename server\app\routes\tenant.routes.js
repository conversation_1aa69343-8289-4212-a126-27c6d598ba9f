import express from 'express';
import * as tenantController from '../controllers/tenant.controller.js';
import { checkRole, verifyAccessToken } from '../middleware/auth.middleware.js';
import { CONSTANTS } from '../utils/constants.utils.js';

const router = express.Router();

/**
 * Tenant Routes
 * Prefix: /api/tenant
 * All routes require admin role
 */

/**
 * @route   GET /api/tenant
 * @desc    Get all tenants (excluding deleted ones)
 * @access  Admin only
 */
router.get(
  '/',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN, CONSTANTS.ROLE.SUPER_ADMIN]),
  tenantController.getAllTenants
);

/**
 * @route   GET /api/tenant/:id
 * @desc    Get tenant by ID
 * @access  Admin only
 */
router.get(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN, CONSTANTS.ROLE.SUPER_ADMIN]),
  tenantController.getTenantById
);

/**
 * @route   POST /api/tenant
 * @desc    Create new tenant
 * @access  Admin only
 */
router.post(
  '/',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN, CONSTANTS.ROLE.SUPER_ADMIN]),
  tenantController.createTenant
);

/**
 * @route   PUT /api/tenant/:id
 * @desc    Update existing tenant
 * @access  Admin only
 */
router.put(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN, CONSTANTS.ROLE.SUPER_ADMIN]),
  tenantController.updateTenant
);

/**
 * @route   DELETE /api/tenant/:id
 * @desc    Soft delete tenant (set is_deleted = true)
 * @access  Admin only
 */
router.delete(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN, CONSTANTS.ROLE.SUPER_ADMIN]),
  tenantController.deleteTenant
);

export default router;
