import { Tenant } from '../models/index.js';
import { dbOperations } from '../utils/db_operations.js';
import { createLogger } from '../utils/logger.utils.js';

const logger = createLogger('TENANT_SERVICE');

/**
 * Tenant Service
 * Business logic for tenant operations
 */
export const tenantService = {
  /**
   * Create a new tenant
   * @param {Object} tenantData - Tenant data
   * @returns {Promise<Object>} Created tenant
   */
  async createTenant(tenantData) {
    try {
      logger.info('Creating new tenant');
      const tenant = await dbOperations.create(Tenant, tenantData);
      return tenant;
    } catch (error) {
      logger.error('Error in createTenant service:', error);
      throw error;
    }
  },

  /**
   * Get all tenants (excluding deleted ones)
   * @returns {Promise<Array>} Array of tenants
   */
  async getAllTenants() {
    try {
      logger.info('Fetching all tenants');
      const tenants = await dbOperations.findAll(Tenant);
      return tenants;
    } catch (error) {
      logger.error('Error in getAllTenants service:', error);
      throw error;
    }
  },

  /**
   * Get tenant by ID
   * @param {number} id - Tenant ID
   * @returns {Promise<Object|null>} Tenant or null
   */
  async getTenantById(id) {
    try {
      logger.info(`Fetching tenant with ID: ${id}`);
      const tenant = await dbOperations.findById(Tenant, id);
      return tenant;
    } catch (error) {
      logger.error(`Error in getTenantById service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Update tenant by ID
   * @param {number} id - Tenant ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated tenant or null
   */
  async updateTenant(id, updateData) {
    try {
      logger.info(`Updating tenant with ID: ${id}`);
      const updatedTenant = await dbOperations.update(Tenant, id, updateData);
      return updatedTenant;
    } catch (error) {
      logger.error(`Error in updateTenant service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Soft delete tenant by ID
   * @param {number} id - Tenant ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteTenant(id) {
    try {
      logger.info(`Soft deleting tenant with ID: ${id}`);
      const success = await dbOperations.softDelete(Tenant, id);
      return success;
    } catch (error) {
      logger.error(`Error in deleteTenant service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Check if tenant exists
   * @param {number} id - Tenant ID
   * @returns {Promise<boolean>} Existence status
   */
  async tenantExists(id) {
    try {
      const exists = await dbOperations.exists(Tenant, id);
      return exists;
    } catch (error) {
      logger.error(`Error checking tenant existence for ID ${id}:`, error);
      throw error;
    }
  },
};

export default tenantService;
