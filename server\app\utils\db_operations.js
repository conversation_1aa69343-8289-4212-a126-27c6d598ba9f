import { createLogger } from './logger.utils.js';

const logger = createLogger('DB_OPERATIONS');

/**
 * Generic Database Operations Utility
 * Provides reusable CRUD functions for any Sequelize model
 */
export const dbOperations = {
  /**
   * Create a new record
   * @param {Object} model - Sequelize model
   * @param {Object} data - Data to create
   * @returns {Promise<Object>} Created record
   */
  async create(model, data) {
    try {
      const record = await model.create(data);
      logger.info(`${model.name} created successfully with ID: ${record.id}`);
      return record;
    } catch (error) {
      logger.error(`Error creating ${model.name}:`, error);
      throw error;
    }
  },

  /**
   * Find all records with optional query conditions
   * @param {Object} model - Sequelize model
   * @param {Object} query - Query options (where, include, order, etc.)
   * @returns {Promise<Array>} Array of records
   */
  async findAll(model, query = {}) {
    try {
      const records = await model.findAll(query);
      logger.info(`Found ${records.length} ${model.name} records`);
      return records;
    } catch (error) {
      logger.error(`Error finding ${model.name} records:`, error);
      throw error;
    }
  },

  /**
   * Find a record by ID with optional query conditions
   * @param {Object} model - Sequelize model
   * @param {number} id - Record ID
   * @param {Object} query - Additional query options
   * @returns {Promise<Object|null>} Found record or null
   */
  async findById(model, id, query = {}) {
    try {
      const record = await model.findOne({
        where: { id, ...query.where },
        ...query
      });
      
      if (record) {
        logger.info(`${model.name} found with ID: ${id}`);
      } else {
        logger.warn(`${model.name} not found with ID: ${id}`);
      }
      
      return record;
    } catch (error) {
      logger.error(`Error finding ${model.name} by ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Update a record by ID
   * @param {Object} model - Sequelize model
   * @param {number} id - Record ID
   * @param {Object} data - Data to update
   * @returns {Promise<Object|null>} Updated record or null
   */
  async update(model, id, data) {
    try {
      // First check if record exists and is not deleted
      const existingRecord = await model.findOne({
        where: { id, is_deleted: false }
      });

      if (!existingRecord) {
        logger.warn(`${model.name} not found or already deleted with ID: ${id}`);
        return null;
      }

      // Update the record
      await model.update(data, {
        where: { id, is_deleted: false }
      });

      // Fetch and return the updated record
      const updatedRecord = await model.findOne({
        where: { id }
      });

      logger.info(`${model.name} updated successfully with ID: ${id}`);
      return updatedRecord;
    } catch (error) {
      logger.error(`Error updating ${model.name} with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Soft delete a record by setting is_deleted = true
   * @param {Object} model - Sequelize model
   * @param {number} id - Record ID
   * @returns {Promise<boolean>} Success status
   */
  async softDelete(model, id) {
    try {
      // First check if record exists and is not already deleted
      const existingRecord = await model.findOne({
        where: { id, is_deleted: false }
      });

      if (!existingRecord) {
        logger.warn(`${model.name} not found or already deleted with ID: ${id}`);
        return false;
      }

      // Soft delete the record
      await model.update(
        { is_deleted: true },
        { where: { id, is_deleted: false } }
      );

      logger.info(`${model.name} soft deleted successfully with ID: ${id}`);
      return true;
    } catch (error) {
      logger.error(`Error soft deleting ${model.name} with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Check if a record exists and is not deleted
   * @param {Object} model - Sequelize model
   * @param {number} id - Record ID
   * @returns {Promise<boolean>} Existence status
   */
  async exists(model, id) {
    try {
      const record = await model.findOne({
        where: { id, is_deleted: false },
        attributes: ['id']
      });
      
      return !!record;
    } catch (error) {
      logger.error(`Error checking existence of ${model.name} with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Count records with optional query conditions
   * @param {Object} model - Sequelize model
   * @param {Object} query - Query options (where conditions)
   * @returns {Promise<number>} Count of records
   */
  async count(model, query = {}) {
    try {
      const count = await model.count(query);
      logger.info(`${model.name} count: ${count}`);
      return count;
    } catch (error) {
      logger.error(`Error counting ${model.name} records:`, error);
      throw error;
    }
  }
};

export default dbOperations;
