import express from 'express';
import * as organizationController from '../controllers/organization.controller.js';
import { checkRole, verifyAccessToken } from '../middleware/auth.middleware.js';
import { CONSTANTS } from '../utils/constants.utils.js';

const router = express.Router();

/**
 * Organization Routes
 * Prefix: /api/organization
 * All routes require admin role
 */

/**
 * @route   GET /api/organization
 * @desc    Get all organizations (excluding deleted ones)
 * @access  Admin only
 */
router.get(
  '/',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  organizationController.getAllOrganizations
);

/**
 * @route   GET /api/organization/:id
 * @desc    Get organization by ID
 * @access  Admin only
 */
router.get(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  organizationController.getOrganizationById
);

/**
 * @route   POST /api/organization
 * @desc    Create new organization
 * @access  Admin only
 */
router.post(
  '/',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  organizationController.createOrganization
);

/**
 * @route   PUT /api/organization/:id
 * @desc    Update existing organization
 * @access  Admin only
 */
router.put(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  organizationController.updateOrganization
);

/**
 * @route   DELETE /api/organization/:id
 * @desc    Soft delete organization (set is_deleted = true)
 * @access  Admin only
 */
router.delete(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  organizationController.deleteOrganization
);

export default router;
