import { permissionService } from '../services/permission.service.js';
import { createLogger } from '../utils/logger.utils.js';
import { 
  STATUS_CODE_SUCCESS,
  STATUS_CODE_CREATED,
  STATUS_CODE_NOT_FOUND,
  STATUS_CODE_INTERNAL_SERVER_STATUS,
  STATUS_CODE_BAD_REQUEST
} from '../utils/status_code.utils.js';

const logger = createLogger('PERMISSION_CONTROLLER');

/**
 * Create a new permission
 */
export const createPermission = async (req, res) => {
  try {
    logger.info('Creating new permission');
    const permissionData = req.body;
    
    const permission = await permissionService.createPermission(permissionData);
    
    return res.status(STATUS_CODE_CREATED).json({
      success: true,
      message: 'Permission created successfully',
      data: permission
    });
  } catch (error) {
    logger.error('Error in createPermission controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get all permissions
 */
export const getAllPermissions = async (req, res) => {
  try {
    logger.info('Fetching all permissions');
    const permissions = await permissionService.getAllPermissions();
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Permissions fetched successfully',
      data: permissions,
      count: permissions.length
    });
  } catch (error) {
    logger.error('Error in getAllPermissions controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get permission by ID
 */
export const getPermissionById = async (req, res) => {
  try {
    const { id } = req.params;
    logger.info(`Fetching permission with ID: ${id}`);
    
    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid permission ID'
      });
    }
    
    const permission = await permissionService.getPermissionById(parseInt(id));
    
    if (!permission) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'Permission not found'
      });
    }
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Permission fetched successfully',
      data: permission
    });
  } catch (error) {
    logger.error('Error in getPermissionById controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Update permission by ID
 */
export const updatePermission = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    logger.info(`Updating permission with ID: ${id}`);
    
    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid permission ID'
      });
    }
    
    const updatedPermission = await permissionService.updatePermission(parseInt(id), updateData);
    
    if (!updatedPermission) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'Permission not found or already deleted'
      });
    }
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Permission updated successfully',
      data: updatedPermission
    });
  } catch (error) {
    logger.error('Error in updatePermission controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Soft delete permission by ID
 */
export const deletePermission = async (req, res) => {
  try {
    const { id } = req.params;
    logger.info(`Soft deleting permission with ID: ${id}`);
    
    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid permission ID'
      });
    }
    
    const success = await permissionService.deletePermission(parseInt(id));
    
    if (!success) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'Permission not found or already deleted'
      });
    }
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Permission deleted successfully'
    });
  } catch (error) {
    logger.error('Error in deletePermission controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
