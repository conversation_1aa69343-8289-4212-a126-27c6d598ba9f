'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('users', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      tenant_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'tenants',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      organization_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'organizations',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      name: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      email: {
        type: Sequelize.STRING(100),
        allowNull: false,
        unique: true,
      },
      password: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      role_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'roles',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      phone_number: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },
      department: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      position: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      avatar: {
        type: Sequelize.TEXT, // or Sequelize.BLOB('long') if storing binary
        allowNull: true,
      },
      last_login: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      created_at: {
        type: 'TIMESTAMPTZ',
        allowNull: false,
        defaultValue: Sequelize.literal('NOW()'),
      },
      updated_at: {
        type: 'TIMESTAMPTZ',
        allowNull: false,
        defaultValue: Sequelize.literal('NOW()'),
      },
      created_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      updated_by: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      is_deleted: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      is_active: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
    });

    // Indexes
    await queryInterface.addIndex('users', ['email'], {
      name: 'users_email_idx',
    });
    await queryInterface.addIndex('users', ['role_id'], {
      name: 'users_role_id_idx',
    });
    await queryInterface.addIndex('users', ['tenant_id'], {
      name: 'users_tenant_id_idx',
    });
    await queryInterface.addIndex('users', ['organization_id'], {
      name: 'users_organization_id_idx',
    });
    await queryInterface.addIndex('users', ['is_active'], {
      name: 'users_is_active_idx',
    });
    await queryInterface.addIndex('users', ['is_deleted'], {
      name: 'users_is_deleted_idx',
    });
    await queryInterface.addIndex('users', ['created_by'], {
      name: 'users_created_by_idx',
    });
    await queryInterface.addIndex('users', ['updated_by'], {
      name: 'users_updated_by_idx',
    });
  },

  async down(queryInterface) {
    await queryInterface.dropTable('users');
  },
};
