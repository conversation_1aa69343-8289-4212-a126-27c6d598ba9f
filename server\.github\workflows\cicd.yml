name: Development Pipeline

on:
  push:
    branches: [develop]
jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [20.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v2
        with:
          node-version: ${{ matrix.node-version }}

      - name: Install dependencies
        run: npm install

      - name: Lint code
        run: npm run lint

      - name: Run tests
        run: npm test

      - name: Upload test coverage
        if: success()
        uses: actions/upload-artifact@v2
        with:
          name: test-coverage
          path: coverage/
