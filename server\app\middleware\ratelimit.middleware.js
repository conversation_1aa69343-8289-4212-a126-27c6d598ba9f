import rateLimit from 'express-rate-limit';
import { CONSTANTS } from '../utils/constants.utils.js';

const windowMinutes = process.env.RATE_LIMIT_WINDOW_MINUTES || 15;
const maxRequests = process.env.RATE_LIMIT_MAX_REQUESTS || CONSTANTS.RATE_LIMIT.MAX_REQUESTS;

/**
 * General rate limiter middleware
 * @returns {Function} - Express middleware function for rate limiting
 */
const rateLimiter = () =>
  rateLimit({
    windowMs: windowMinutes * CONSTANTS.TIME_CONSTANTS.MILLISECONDS_MULTIPLIER,
    max: maxRequests,
    message: { error: CONSTANTS.RATE_LIMIT.MESSAGE },
    headers: true,
    standardHeaders: true,
    legacyHeaders: false,
  });

/**
 * Strict rate limiter for authentication endpoints
 * @returns {Function} - Express middleware function for auth rate limiting
 */
const authRateLimiter = () =>
  rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: maxRequests, 
    message: { 
      error: 'Too many login attempts. Please try again later.',
      retryAfter: '15 minutes'
    },
    headers: true,
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: true, // Don't count successful requests
  });

/**
 * Signup rate limiter to prevent spam
 * @returns {Function} - Express middleware function for signup rate limiting
 */
const signupRateLimiter = () =>
  rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: maxRequests, // 3 signup attempts per hour
    message: { 
      error: 'Too many signup attempts. Please try again later.',
      retryAfter: '1 hour'
    },
    headers: true,
    standardHeaders: true,
    legacyHeaders: false,
  });

export { rateLimiter as default, authRateLimiter, signupRateLimiter };
