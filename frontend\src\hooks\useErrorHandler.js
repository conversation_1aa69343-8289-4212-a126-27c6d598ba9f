"use client";

import { useState, useCallback, useContext, createContext } from "react";
import { useToast } from "@/components/ui/toast";

// Error Context
const ErrorContext = createContext();

/**
 * Error Provider Component
 */
export const ErrorProvider = ({ children, onError, reportError }) => {
  const [errors, setErrors] = useState([]);
  const { addToast } = useToast();

  const handleError = useCallback((error, context = {}) => {
    const errorInfo = {
      id: Date.now().toString(),
      message: error.message || "An error occurred",
      stack: error.stack,
      timestamp: new Date().toISOString(),
      context,
      type: error.name || "Error",
    };

    // Add to errors list
    setErrors(prev => [...prev, errorInfo]);

    // Show toast notification
    addToast(errorInfo.message, "error");

    // Call custom error handler
    if (onError) {
      onError(errorInfo);
    }

    // Report error to monitoring service
    if (reportError) {
      reportError(errorInfo);
    }

    return errorInfo.id;
  }, [onError, reportError, addToast]);

  const clearError = useCallback((errorId) => {
    setErrors(prev => prev.filter(error => error.id !== errorId));
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrors([]);
  }, []);

  const value = {
    errors,
    handleError,
    clearError,
    clearAllErrors,
  };

  return (
    <ErrorContext.Provider value={value}>
      {children}
    </ErrorContext.Provider>
  );
};

/**
 * Hook for error handling
 * @returns {Object} Error handling utilities
 */
export const useErrorHandler = () => {
  const context = useContext(ErrorContext);
  const { addToast } = useToast();

  if (!context) {
    // Fallback if not wrapped in ErrorProvider
    return {
      errors: [],
      handleError: (error, context = {}) => {
        console.error("Error:", error, context);
        addToast(error.message || "An error occurred", "error");
      },
      clearError: () => {},
      clearAllErrors: () => {},
    };
  }

  return context;
};

/**
 * Hook for async error handling
 * @returns {Object} Async error handling utilities
 */
export const useAsyncError = () => {
  const { handleError } = useErrorHandler();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const executeAsync = useCallback(async (asyncFunction, context = {}) => {
    setLoading(true);
    setError(null);

    try {
      const result = await asyncFunction();
      setLoading(false);
      return result;
    } catch (err) {
      setError(err);
      setLoading(false);
      handleError(err, context);
      throw err;
    }
  }, [handleError]);

  const reset = useCallback(() => {
    setError(null);
    setLoading(false);
  }, []);

  return {
    executeAsync,
    loading,
    error,
    reset,
  };
};

/**
 * Hook for form error handling
 * @returns {Object} Form error handling utilities
 */
export const useFormErrors = () => {
  const [fieldErrors, setFieldErrors] = useState({});
  const [generalError, setGeneralError] = useState(null);

  const setFieldError = useCallback((field, error) => {
    setFieldErrors(prev => ({
      ...prev,
      [field]: error,
    }));
  }, []);

  const clearFieldError = useCallback((field) => {
    setFieldErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const setFormError = useCallback((error) => {
    setGeneralError(error);
  }, []);

  const clearFormError = useCallback(() => {
    setGeneralError(null);
  }, []);

  const clearAllErrors = useCallback(() => {
    setFieldErrors({});
    setGeneralError(null);
  }, []);

  const hasErrors = Object.keys(fieldErrors).length > 0 || generalError !== null;

  return {
    fieldErrors,
    generalError,
    setFieldError,
    clearFieldError,
    setFormError,
    clearFormError,
    clearAllErrors,
    hasErrors,
  };
};

/**
 * Hook for API error handling
 * @returns {Object} API error handling utilities
 */
export const useAPIError = () => {
  const { handleError } = useErrorHandler();

  const handleAPIError = useCallback((error, context = {}) => {
    let errorMessage = "An error occurred";
    let errorType = "UNKNOWN_ERROR";

    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      errorMessage = data?.message || data?.error || `Server error (${status})`;
      
      switch (status) {
        case 400:
          errorType = "BAD_REQUEST";
          break;
        case 401:
          errorType = "UNAUTHORIZED";
          errorMessage = "Please log in to continue";
          break;
        case 403:
          errorType = "FORBIDDEN";
          errorMessage = "You don't have permission to perform this action";
          break;
        case 404:
          errorType = "NOT_FOUND";
          errorMessage = "The requested resource was not found";
          break;
        case 422:
          errorType = "VALIDATION_ERROR";
          break;
        case 500:
          errorType = "SERVER_ERROR";
          errorMessage = "Internal server error. Please try again later.";
          break;
        default:
          errorType = "HTTP_ERROR";
      }
    } else if (error.request) {
      // Request was made but no response received
      errorType = "NETWORK_ERROR";
      errorMessage = "Network error. Please check your connection.";
    } else {
      // Something else happened
      errorMessage = error.message || errorMessage;
    }

    const enhancedError = {
      ...error,
      message: errorMessage,
      type: errorType,
    };

    handleError(enhancedError, {
      ...context,
      isAPIError: true,
      status: error.response?.status,
    });

    return enhancedError;
  }, [handleError]);

  return { handleAPIError };
};

/**
 * Hook for retry logic with error handling
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} delay - Delay between retries in ms
 * @returns {Object} Retry utilities
 */
export const useRetry = (maxRetries = 3, delay = 1000) => {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const { handleError } = useErrorHandler();

  const executeWithRetry = useCallback(async (asyncFunction, context = {}) => {
    let lastError;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 0) {
          setIsRetrying(true);
          await new Promise(resolve => setTimeout(resolve, delay * attempt));
        }

        const result = await asyncFunction();
        setRetryCount(0);
        setIsRetrying(false);
        return result;
      } catch (error) {
        lastError = error;
        setRetryCount(attempt + 1);

        // Don't retry on client errors (4xx)
        if (error.response?.status >= 400 && error.response?.status < 500) {
          break;
        }

        if (attempt === maxRetries) {
          break;
        }
      }
    }

    setIsRetrying(false);
    handleError(lastError, {
      ...context,
      retryCount,
      maxRetries,
    });
    throw lastError;
  }, [maxRetries, delay, retryCount, handleError]);

  const reset = useCallback(() => {
    setRetryCount(0);
    setIsRetrying(false);
  }, []);

  return {
    executeWithRetry,
    retryCount,
    isRetrying,
    reset,
    canRetry: retryCount < maxRetries,
  };
};

/**
 * Error boundary hook for functional components
 * @returns {Function} Error throwing function
 */
export const useErrorBoundary = () => {
  const [, setError] = useState();

  return useCallback((error) => {
    setError(() => {
      throw error;
    });
  }, []);
};
