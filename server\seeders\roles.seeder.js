const roles = [
  {
    name: 'admin',
    description: 'Super Admin',
    created_at: new Date(),
    updated_at: new Date(),
    created_by: null, // First role, so no creator
    updated_by: null,
  },
  {
    name: 'org_head',
    description: 'Organization Head',
    created_at: new Date(),
    updated_at: new Date(),
    created_by: null,
    updated_by: null,
  },
  {
    name: 'emp',
    description: 'Employee',
    created_at: new Date(),
    updated_at: new Date(),
    created_by: null,
    updated_by: null,
  },
];

export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert('roles', roles);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('roles', null);
  }
};
