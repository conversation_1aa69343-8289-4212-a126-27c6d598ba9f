export default {
  async up(queryInterface, Sequelize) {
    const roles = [
      {
        name: 'admin',
        description: 'Super Admin',
        is_active: true,
        level: 'system', // optional custom level
        department: null,
        permission_count: 0,
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: false,
      },
      {
        name: 'org_head',
        description: 'Organization Head',
        is_active: true,
        level: 'organization',
        department: null,
        permission_count: 0,
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: false,
      },
      {
        name: 'tenant_head',
        description: 'Tenant Head',
        is_active: true,
        level: 'tenant',
        department: null,
        permission_count: 0,
        created_at: new Date(),
        updated_at: new Date(),
        is_deleted: false,
      },
    ];

    await queryInterface.bulkInsert('roles', roles);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('roles', {
      name: ['admin', 'org_head', 'tenant_head'],
    });
  },
};
