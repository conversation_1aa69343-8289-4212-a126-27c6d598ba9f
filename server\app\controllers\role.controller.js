import { roleService } from '../services/role.service.js';
import { createLogger } from '../utils/logger.utils.js';
import { 
  STATUS_CODE_SUCCESS,
  STATUS_CODE_CREATED,
  STATUS_CODE_NOT_FOUND,
  STATUS_CODE_INTERNAL_SERVER_STATUS,
  STATUS_CODE_BAD_REQUEST
} from '../utils/status_code.utils.js';

const logger = createLogger('ROLE_CONTROLLER');

/**
 * Create a new role
 */
export const createRole = async (req, res) => {
  try {
    logger.info('Creating new role');
    const roleData = req.body;
    
    const role = await roleService.createRole(roleData);
    
    return res.status(STATUS_CODE_CREATED).json({
      success: true,
      message: 'Role created successfully',
      data: role
    });
  } catch (error) {
    logger.error('Error in createRole controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get all roles
 */
export const getAllRoles = async (req, res) => {
  try {
    logger.info('Fetching all roles');
    const roles = await roleService.getAllRoles();
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Roles fetched successfully',
      data: roles,
      count: roles.length
    });
  } catch (error) {
    logger.error('Error in getAllRoles controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Get role by ID
 */
export const getRoleById = async (req, res) => {
  try {
    const { id } = req.params;
    logger.info(`Fetching role with ID: ${id}`);
    
    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid role ID'
      });
    }
    
    const role = await roleService.getRoleById(parseInt(id));
    
    if (!role) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'Role not found'
      });
    }
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Role fetched successfully',
      data: role
    });
  } catch (error) {
    logger.error('Error in getRoleById controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Update role by ID
 */
export const updateRole = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    logger.info(`Updating role with ID: ${id}`);
    
    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid role ID'
      });
    }
    
    const updatedRole = await roleService.updateRole(parseInt(id), updateData);
    
    if (!updatedRole) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'Role not found or already deleted'
      });
    }
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Role updated successfully',
      data: updatedRole
    });
  } catch (error) {
    logger.error('Error in updateRole controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};

/**
 * Soft delete role by ID
 */
export const deleteRole = async (req, res) => {
  try {
    const { id } = req.params;
    logger.info(`Soft deleting role with ID: ${id}`);
    
    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid role ID'
      });
    }
    
    const success = await roleService.deleteRole(parseInt(id));
    
    if (!success) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'Role not found or already deleted'
      });
    }
    
    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Role deleted successfully'
    });
  } catch (error) {
    logger.error('Error in deleteRole controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error'
    });
  }
};
