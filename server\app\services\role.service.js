import { Role } from '../models/index.js';
import { dbOperations } from '../utils/db_operations.js';
import { createLogger } from '../utils/logger.utils.js';

const logger = createLogger('ROLE_SERVICE');

/**
 * Role Service
 * Business logic for role operations
 */
export const roleService = {
  /**
   * Create a new role
   * @param {Object} roleData - Role data
   * @returns {Promise<Object>} Created role
   */
  async createRole(roleData) {
    try {
      logger.info('Creating new role');
      const role = await dbOperations.create(Role, roleData);
      return role;
    } catch (error) {
      logger.error('Error in createRole service:', error);
      throw error;
    }
  },

  /**
   * Get all roles (excluding deleted ones)
   * @returns {Promise<Array>} Array of roles
   */
  async getAllRoles() {
    try {
      logger.info('Fetching all roles');
      const roles = await dbOperations.findAll(Role, {
        where: { is_deleted: false },
        order: [['created_at', 'DESC']]
      });
      return roles;
    } catch (error) {
      logger.error('Error in getAllRoles service:', error);
      throw error;
    }
  },

  /**
   * Get role by ID
   * @param {number} id - Role ID
   * @returns {Promise<Object|null>} Role or null
   */
  async getRoleById(id) {
    try {
      logger.info(`Fetching role with ID: ${id}`);
      const role = await dbOperations.findById(Role, id, {
        where: { is_deleted: false }
      });
      return role;
    } catch (error) {
      logger.error(`Error in getRoleById service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Update role by ID
   * @param {number} id - Role ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object|null>} Updated role or null
   */
  async updateRole(id, updateData) {
    try {
      logger.info(`Updating role with ID: ${id}`);
      const updatedRole = await dbOperations.update(Role, id, updateData);
      return updatedRole;
    } catch (error) {
      logger.error(`Error in updateRole service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Soft delete role by ID
   * @param {number} id - Role ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteRole(id) {
    try {
      logger.info(`Soft deleting role with ID: ${id}`);
      const success = await dbOperations.softDelete(Role, id);
      return success;
    } catch (error) {
      logger.error(`Error in deleteRole service for ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Check if role exists
   * @param {number} id - Role ID
   * @returns {Promise<boolean>} Existence status
   */
  async roleExists(id) {
    try {
      const exists = await dbOperations.exists(Role, id);
      return exists;
    } catch (error) {
      logger.error(`Error checking role existence for ID ${id}:`, error);
      throw error;
    }
  }
};

export default roleService;
