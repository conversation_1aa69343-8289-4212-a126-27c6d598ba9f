import passport from 'passport';
import { Strategy as Auth0Strategy } from 'passport-auth0';
import logger from './logger.config.js';
import { CONSTANTS } from '../utils/constants.utils.js';

/**
 * Configure Auth0 Strategy for Passport
 * <AUTHOR>
 */
export const configureAuth0 = () => {
  try {
    // Check if Auth0 environment variables are configured
    const ssoIssuer = process.env.SSO_ISSUER;
    const ssoClientId = process.env.SSO_CLIENT_ID;
    const ssoClientSecret = process.env.SSO_CLIENT_SECRET;
    const ssoRedirectUri = process.env.SSO_REDIRECT_URI;

    // If Auth0 is not configured, skip initialization
    if (!ssoIssuer || !ssoClientId || !ssoClientSecret || !ssoRedirectUri) {
      logger.warn('Auth0 configuration not found. SSO authentication will be disabled.');
      return false;
    }

    // Configure Auth0 strategy
    passport.use(
      new Auth0Strategy(
        {
          domain: ssoIssuer.replace('https://', ''),
          clientID: ssoClientId,
          clientSecret: ssoClientSecret,
          callbackURL: ssoRedirectUri,
        },
        (accessToken, refreshToken, extraParams, profile, done) => {
          return done(null, profile);
        }
      )
    );

    // Configure session serialization
    passport.serializeUser((user, done) => done(null, user));
    passport.deserializeUser((user, done) => done(null, user));

    logger.info(CONSTANTS.AUTH0.AUTH_CONFIG_SUCCESS);
    return true;
  } catch (error) {
    logger.error(CONSTANTS.AUTH0.AUTH_CONFIG_ERROR, error);
    logger.warn('Auth0 configuration failed. SSO authentication will be disabled.');
    return false;
  }
};
