'use strict';

/** @type {import('sequelize-cli').Migration} */
export default {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('tokens', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      refreshToken: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      refreshTokenExpiresAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      resetToken: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      resetTokenExpiresAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      used: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      revoked: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      createdAt: {
        allowNull: false,
        type: 'TIMESTAMPTZ',
        defaultValue: Sequelize.literal('NOW()'),
      },
      updatedAt: {
        allowNull: false,
        type: 'TIMESTAMPTZ',
        defaultValue: Sequelize.literal('NOW()'),
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      updatedBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
    });

    // Add indexes for better performance
    await queryInterface.addIndex('tokens', ['userId'], {
      name: 'tokens_user_id_idx',
    });
    await queryInterface.addIndex('tokens', ['refreshToken'], {
      name: 'tokens_refresh_token_idx',
    });
    await queryInterface.addIndex('tokens', ['resetToken'], {
      name: 'tokens_reset_token_idx',
    });
    await queryInterface.addIndex('tokens', ['refreshTokenExpiresAt'], {
      name: 'tokens_refresh_expires_idx',
    });
    await queryInterface.addIndex('tokens', ['resetTokenExpiresAt'], {
      name: 'tokens_reset_expires_idx',
    });
    await queryInterface.addIndex('tokens', ['revoked'], {
      name: 'tokens_revoked_idx',
    });
    await queryInterface.addIndex('tokens', ['createdBy'], {
      name: 'tokens_created_by_idx',
    });
    await queryInterface.addIndex('tokens', ['updatedBy'], {
      name: 'tokens_updated_by_idx',
    });
  },

  async down(queryInterface) {
    await queryInterface.dropTable('tokens');
  },
};
