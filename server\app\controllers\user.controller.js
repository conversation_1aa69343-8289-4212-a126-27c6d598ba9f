import * as userService from '../services/user.service.js';
import { userService as newUserService } from '../services/user.service.js';
import * as authService from '../services/auth.service.js';
import { CONSTANTS } from '../utils/constants.utils.js';
import * as status from '../utils/status_code.utils.js';
import {
  STATUS_CODE_SUCCESS,
  STATUS_CODE_CREATED,
  STATUS_CODE_NOT_FOUND,
  STATUS_CODE_INTERNAL_SERVER_STATUS,
  STATUS_CODE_BAD_REQUEST,
} from '../utils/status_code.utils.js';
import { errorResponse, successResponse } from '../utils/response.util.js';
import logger from '../config/logger.config.js';
import { createLogger } from '../utils/logger.utils.js';
import { LOG_MESSAGES } from '../utils/log_messages.utils.js'; // Corrected path for LOG_MESSAGES
import { validateRequest, handleError } from '../utils/validation.utils.js';
import { hashPassword } from '../utils/password.utils.js';

const controllerLogger = createLogger('USER_CONTROLLER');

/**
 * @param {*} req
 * @param {*} res
 * @returns
 * @description Create a new user
 * @api /api/users
 * @method POST
 */
export const createUser = async (req, res) => {
  try {
    if (validateRequest(req)) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(validateRequest(req)));
    }

    logger.info(LOG_MESSAGES.USER.CREATING);

    const { role, name, phone_number, ...userData } = req.body;

    userData.role_id = role;
    // Handle required and optional fields
    userData.name = name; // Required field
    userData.phone_number = phone_number || null; // Optional field

    if (!(await authService.checkRoleExistsById(role))) {
      logger.warn(LOG_MESSAGES.ROLE.INVALID_ROLE);

      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.ROLE.INVALID_PROVIDED));
    }

    if (await authService.checkUserExists(userData.email)) {
      logger.warn(LOG_MESSAGES.USER.ALREADY_EXISTS);

      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.USER.ALREADY_EXISTS_ERROR));
    }

    userData.password = await hashPassword(userData.password);

    // Define `newUser` by calling the user creation service
    const newUser = await userService.createUser(userData);

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(CONSTANTS.USER.CREATED_SUCCESSFULLY, { newUser }));
  } catch (error) {
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.USER.ERROR_CREATING
    );

    return res.status(errorStatus).json(response);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns
 * @description Get all users
 * @api /api/users
 * @method GET
 */
export const getAllUsers = async (req, res) => {
  console.log('getAllUsers');
  try {
    if (validateRequest(req)) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(validateRequest(req)));
    }

    logger.info(LOG_MESSAGES.USER.FETCHING_ALL);

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(
        successResponse(
          CONSTANTS.USER.FETCHED_SUCCESSFULLY,
          await userService.getAllUsers()
        )
      );
  } catch (error) {
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.USER.ERROR.FETCHING_ALL
    );

    return res.status(errorStatus).json(response);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns
 * @description Get user by ID
 * @api /api/users/:id
 * @method GET
 */
export const getUserById = async (req, res) => {
  try {
    if (validateRequest(req)) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(validateRequest(req)));
    }

    logger.info(LOG_MESSAGES.USER.FETCHING_BY_ID);

    const user = await userService.getUserById(req.params.id);

    if (!user) {
      logger.info(LOG_MESSAGES.USER.NOT_FOUND);

      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.USER.NOT_FOUND));
    }

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(CONSTANTS.USER.USER_FETCHED_SUCCESSFULLY, user));
  } catch (error) {
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.USER.ERROR_FETCHING_BY_ID
    );

    return res.status(errorStatus).json(response);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns
 * @description Update user by ID
 * @api /api/users/:id
 * @method PUT
 */
export const updateUser = async (req, res) => {
  try {
    if (validateRequest(req)) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(validateRequest(req)));
    }

    logger.info(LOG_MESSAGES.USER.UPDATING);

    const userData = req.body;

    if (userData.password) {
      userData.password = await hashPassword(userData.password);
    }

    const updatedUser = await userService.updateUser(req.params.id, userData);

    if (!updatedUser) {
      logger.info(LOG_MESSAGES.USER.NOT_FOUND);

      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.USER.NOT_FOUND));
    }

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(CONSTANTS.USER.UPDATED_SUCCESSFULLY, updatedUser));
  } catch (error) {
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.USER.ERROR_UPDATING
    );

    return res.status(errorStatus).json(response);
  }
};

/**
 * @param {*} req
 * @param {*} res
 * @returns
 * @description Delete user by ID
 * @api /api/users/:id
 * @method DELETE
 */
export const deleteUser = async (req, res) => {
  try {
    if (validateRequest(req)) {
      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(validateRequest(req)));
    }

    logger.info(LOG_MESSAGES.USER.DELETING);

    const deletedUser = await userService.deleteUser(req.params.id);

    if (!deletedUser) {
      logger.info(LOG_MESSAGES.USER.NOT_FOUND);

      return res
        .status(status.STATUS_CODE_BAD_REQUEST)
        .json(errorResponse(CONSTANTS.USER.NOT_FOUND));
    }

    return res
      .status(status.STATUS_CODE_SUCCESS)
      .json(successResponse(CONSTANTS.USER.DELETED_SUCCESSFULLY));
  } catch (error) {
    const { status: errorStatus, response } = handleError(
      error,
      LOG_MESSAGES.USER.ERROR_DELETING
    );

    return res.status(errorStatus).json(response);
  }
};

// New CRUD Pattern Controllers
/**
 * Create a new user (new pattern)
 */
export const createUserNew = async (req, res) => {
  try {
    controllerLogger.info('Creating new user (new pattern)');
    const userData = req.body;

    // Hash password if provided
    if (userData.password) {
      userData.password = await hashPassword(userData.password);
    }

    const user = await newUserService.createUser(userData);

    return res.status(STATUS_CODE_CREATED).json({
      success: true,
      message: 'User created successfully',
      data: user,
    });
  } catch (error) {
    controllerLogger.error('Error in createUserNew controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

/**
 * Get all users (new pattern)
 */
export const getAllUsersNew = async (req, res) => {
  try {
    controllerLogger.info('Fetching all users (new pattern)');
    const users = await newUserService.getAllUsers();

    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'Users fetched successfully',
      data: users,
      count: users.length,
    });
  } catch (error) {
    controllerLogger.error('Error in getAllUsersNew controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

/**
 * Get user by ID (new pattern)
 */
export const getUserByIdNew = async (req, res) => {
  try {
    const { id } = req.params;
    controllerLogger.info(`Fetching user with ID: ${id} (new pattern)`);

    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid user ID',
      });
    }

    const user = await newUserService.getUserById(parseInt(id));

    if (!user) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'User not found',
      });
    }

    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'User fetched successfully',
      data: user,
    });
  } catch (error) {
    controllerLogger.error('Error in getUserByIdNew controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

/**
 * Update user by ID (new pattern)
 */
export const updateUserNew = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    controllerLogger.info(`Updating user with ID: ${id} (new pattern)`);

    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid user ID',
      });
    }

    // Hash password if provided
    if (updateData.password) {
      updateData.password = await hashPassword(updateData.password);
    }

    const updatedUser = await newUserService.updateUser(
      parseInt(id),
      updateData
    );

    if (!updatedUser) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'User not found or already deleted',
      });
    }

    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'User updated successfully',
      data: updatedUser,
    });
  } catch (error) {
    controllerLogger.error('Error in updateUserNew controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error',
    });
  }
};

/**
 * Soft delete user by ID (new pattern)
 */
export const deleteUserNew = async (req, res) => {
  try {
    const { id } = req.params;
    controllerLogger.info(`Soft deleting user with ID: ${id} (new pattern)`);

    if (!id || isNaN(id)) {
      return res.status(STATUS_CODE_BAD_REQUEST).json({
        success: false,
        message: 'Invalid user ID',
      });
    }

    const success = await newUserService.deleteUser(parseInt(id));

    if (!success) {
      return res.status(STATUS_CODE_NOT_FOUND).json({
        success: false,
        message: 'User not found or already deleted',
      });
    }

    return res.status(STATUS_CODE_SUCCESS).json({
      success: true,
      message: 'User deleted successfully',
    });
  } catch (error) {
    controllerLogger.error('Error in deleteUserNew controller:', error);
    return res.status(STATUS_CODE_INTERNAL_SERVER_STATUS).json({
      success: false,
      message: 'Internal server error',
    });
  }
};
