import express from 'express';
import * as permissionController from '../controllers/permission.controller.js';
import { checkRole, verifyAccessToken } from '../middleware/auth.middleware.js';
import { CONSTANTS } from '../utils/constants.utils.js';

const router = express.Router();

/**
 * Permission Routes
 * Prefix: /api/permission
 * All routes require admin role
 */

/**
 * @route   GET /api/permission
 * @desc    Get all permissions (excluding deleted ones)
 * @access  Admin only
 */
router.get(
  '/',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  permissionController.getAllPermissions
);

/**
 * @route   GET /api/permission/:id
 * @desc    Get permission by ID
 * @access  Admin only
 */
router.get(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  permissionController.getPermissionById
);

/**
 * @route   POST /api/permission
 * @desc    Create new permission
 * @access  Admin only
 */
router.post(
  '/',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  permissionController.createPermission
);

/**
 * @route   PUT /api/permission/:id
 * @desc    Update existing permission
 * @access  Admin only
 */
router.put(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  permissionController.updatePermission
);

/**
 * @route   DELETE /api/permission/:id
 * @desc    Soft delete permission (set is_deleted = true)
 * @access  Admin only
 */
router.delete(
  '/:id',
  verifyAccessToken,
  checkRole([CONSTANTS.ROLE.ADMIN]),
  permissionController.deletePermission
);

export default router;
