import { Token } from '../models/index.js';
import { generateAccessToken, generateRefreshToken, generateResetToken } from '../utils/jwt.utils.js';
import logger from '../config/logger.config.js';
import { LOG_MESSAGES } from '../utils/log_messages.utils.js';
import { Op } from 'sequelize';

/**
 * Parse time string to milliseconds
 * @param {string} timeString - Time string (e.g., '15m', '1h', '7d')
 * @returns {number} Milliseconds
 */
const parseTimeToMs = (timeString) => {
  if (!timeString || typeof timeString !== 'string') {
    return 15 * 60 * 1000; // Default 15 minutes
  }

  const unit = timeString.slice(-1);
  const value = parseInt(timeString.slice(0, -1), 10);

  if (isNaN(value)) {
    return 15 * 60 * 1000; // Default 15 minutes
  }

  switch (unit) {
    case 's':
      return value * 1000;
    case 'm':
      return value * 60 * 1000;
    case 'h':
      return value * 60 * 60 * 1000;
    case 'd':
      return value * 24 * 60 * 60 * 1000;
    default:
      return 15 * 60 * 1000; // Default 15 minutes
  }
};

/**
 * Generate and store access token
 * @param {Object} user - User object
 * @returns {string} Access token
 */
export const createAccessToken = async (user) => {
  try {
    const payload = {
      id: user.id,
      email: user.email,
      role_id: user.role_id,
    };

    const token = generateAccessToken(payload);
    // Access tokens are not stored in the database, only refresh and reset tokens
    logger.info(LOG_MESSAGES.TOKEN.ACCESS_TOKEN_CREATED(user.id));
    return token;
  } catch (error) {
    logger.error(LOG_MESSAGES.TOKEN.ACCESS_TOKEN_CREATION_FAILED(error.message));
    throw error;
  }
};

/**
 * Generate and store refresh token
 * @param {Object} user - User object
 * @returns {string} Refresh token
 */
export const createRefreshToken = async (user) => {
  try {
    const payload = {
      id: user.id,
      email: user.email,
      role_id: user.role_id,
    };

    const token = generateRefreshToken(payload);
    const expiresAt = new Date(Date.now() + parseTimeToMs(process.env.JWT_REFRESH_EXPIRY || '7d'));

    await Token.create({
      userId: user.id,
      refreshToken: token,
      refreshTokenExpiresAt: expiresAt,
      revoked: false,
      used: false,
    });

    logger.info(LOG_MESSAGES.TOKEN.REFRESH_TOKEN_CREATED(user.id));
    return token;
  } catch (error) {
    logger.error(LOG_MESSAGES.TOKEN.REFRESH_TOKEN_CREATION_FAILED(error.message));
    throw error;
  }
};

/**
 * Generate and store reset token
 * @param {Object} user - User object
 * @returns {string} Reset token
 */
export const createResetToken = async (user) => {
  try {
    const payload = {
      id: user.id,
      email: user.email,
    };

    const token = generateResetToken(payload);
    const expiresAt = new Date(Date.now() + parseTimeToMs(process.env.JWT_RESET_EXPIRY || '1h'));

    await Token.create({
      userId: user.id,
      resetToken: token,
      resetTokenExpiresAt: expiresAt,
      revoked: false,
      used: false,
    });

    logger.info(LOG_MESSAGES.TOKEN.RESET_TOKEN_CREATED(user.id));
    return token;
  } catch (error) {
    logger.error(LOG_MESSAGES.TOKEN.RESET_TOKEN_CREATION_FAILED(error.message));
    throw error;
  }
};

/**
 * Validate token from database
 * @param {string} token - Token to validate
 * @param {string} tokenType - Type of token (refresh, reset)
 * @returns {Object|null} Token record or null if invalid
 */
export const validateToken = async (token, tokenType) => {
  try {
    let whereClause = {
      revoked: false,
      used: false,
    };

    if (tokenType === 'refresh') {
      whereClause.refreshToken = token;
      whereClause.refreshTokenExpiresAt = {
        [Op.gt]: new Date(),
      };
    } else if (tokenType === 'reset') {
      whereClause.resetToken = token;
      whereClause.resetTokenExpiresAt = {
        [Op.gt]: new Date(),
      };
    }

    const tokenRecord = await Token.findOne({
      where: whereClause,
    });

    return tokenRecord;
  } catch (error) {
    logger.error(LOG_MESSAGES.TOKEN.VALIDATION_FAILED(error.message));
    return null;
  }
};

/**
 * Revoke token
 * @param {string} token - Token to revoke
 * @param {string} tokenType - Type of token (refresh, reset)
 * @returns {boolean} Success status
 */
export const revokeToken = async (token, tokenType) => {
  try {
    let whereClause = {};

    if (tokenType === 'refresh') {
      whereClause.refreshToken = token;
    } else if (tokenType === 'reset') {
      whereClause.resetToken = token;
    }

    const result = await Token.update(
      { revoked: true },
      { where: whereClause }
    );

    logger.info(LOG_MESSAGES.TOKEN.TOKEN_REVOKED(tokenType));
    return result[0] > 0;
  } catch (error) {
    logger.error(LOG_MESSAGES.TOKEN.TOKEN_REVOCATION_FAILED(error.message));
    return false;
  }
};

/**
 * Revoke all tokens for a user
 * @param {number} userId - User ID
 * @param {string} tokenType - Type of token (refresh, reset) (optional)
 * @returns {boolean} Success status
 */
export const revokeAllUserTokens = async (userId, tokenType = null) => {
  try {
    const whereClause = {
      userId: userId,
      revoked: false,
    };

    const result = await Token.update(
      { revoked: true },
      { where: whereClause }
    );

    logger.info(LOG_MESSAGES.TOKEN.ALL_TOKENS_REVOKED(userId, tokenType));
    return result[0] > 0;
  } catch (error) {
    logger.error(LOG_MESSAGES.TOKEN.TOKEN_REVOCATION_FAILED(error.message));
    return false;
  }
};

/**
 * Clean up expired tokens
 * @returns {number} Number of tokens cleaned up
 */
export const cleanupExpiredTokens = async () => {
  try {
    const result = await Token.destroy({
      where: {
        [Op.or]: [
          {
            refreshTokenExpiresAt: {
              [Op.lt]: new Date(),
            },
          },
          {
            resetTokenExpiresAt: {
              [Op.lt]: new Date(),
            },
          },
        ],
      },
    });

    logger.info(LOG_MESSAGES.TOKEN.EXPIRED_TOKENS_CLEANED(result));
    return result;
  } catch (error) {
    logger.error(LOG_MESSAGES.TOKEN.TOKEN_CLEANUP_FAILED(error.message));
    return 0;
  }
};

/**
 * Get user tokens
 * @param {number} userId - User ID
 * @param {string} tokenType - Type of token (refresh, reset) (optional)
 * @returns {Array} Array of token records
 */
export const getUserTokens = async (userId, tokenType = null) => {
  try {
    const whereClause = {
      userId: userId,
      revoked: false,
    };

    const tokens = await Token.findAll({
      where: whereClause,
      order: [['createdAt', 'DESC']],
    });

    return tokens;
  } catch (error) {
    logger.error(LOG_MESSAGES.TOKEN.GET_USER_TOKENS_FAILED(error.message));
    return [];
  }
}; 