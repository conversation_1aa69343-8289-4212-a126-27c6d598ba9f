import express from 'express';
import cookieParser from 'cookie-parser';
import session from 'express-session';
import passport from 'passport';
import cors from 'cors';
import morganMiddleware from './middleware/morgan.middleware.js';
import mainRoutes from './routes/index.js';
import logger from './config/logger.config.js';
import { CONSTANTS } from './utils/constants.utils.js';
import * as status from './utils/status_code.utils.js';
import rateLimiter from './middleware/ratelimit.middleware.js';
import { sessionConfig } from './config/security.config.js';

const app = express();
const {
  FRONTEND_URL,
  SERVER_URL,
  NEXT_PUBLIC_API_URL,
  SSO_REDIRECT_URI,
  AZURE_BASE_URL,
  COOKIE_DOMAIN,
} = process.env;

// CORS configuration
const corsOptions = {
  origin: (origin, callback) => {
    if (!origin) return callback(null, true);

    // Collect all URL-related environment variables
    const allowedOrigins = [
      FRONTEND_URL,
      SERVER_URL,
      NEXT_PUBLIC_API_URL,
      SSO_REDIRECT_URI,
      AZURE_BASE_URL,
      // Add the specific domain that's causing issues
      'https://cpa-dashboard.getondataconsulting.in',
      'http://cpa-dashboard.getondataconsulting.in',
    ].filter(Boolean);

    // Also allow any subdomain of getondataconsulting.in
    const isGetOnDataConsultingDomain = origin.includes(
      'getondataconsulting.in'
    );

    if (allowedOrigins.includes(origin) || isGetOnDataConsultingDomain) {
      logger.info(`CORS: Allowing origin ${origin}`);
      callback(null, true);
    } else {
      logger.warn(
        `CORS: Blocking origin ${origin}. Allowed origins: ${allowedOrigins.join(', ')}`
      );
      logger.warn(
        `CORS: Environment variables - FRONTEND_URL: ${FRONTEND_URL}, SERVER_URL: ${SERVER_URL}`
      );
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Access-Control-Request-Method',
    'Access-Control-Request-Headers',
  ],
  exposedHeaders: ['Content-Range', 'X-Content-Range'],
  credentials: true,
  maxAge: 86400,
  preflightContinue: false,
  optionsSuccessStatus: 204,
};

// Session configuration
const sessionOptions = {
  ...sessionConfig,
  cookie: {
    secure: true,
    httpOnly: true,
    sameSite: 'strict',
    maxAge: CONSTANTS.TIME_CONSTANTS.COOKIE_AGE,
  },
};

// Middleware setup
app.use(rateLimiter());
app.use(cors(corsOptions));
app.use(express.json({ limit: '10kb' }));
app.use(express.urlencoded({ extended: true, limit: '10kb' }));
app.use(cookieParser());
app.use(session(sessionOptions));
app.use(passport.initialize());
app.use(passport.session());
app.use(morganMiddleware);

// Request logging middleware
app.use((req, res, next) => {
  logger.info(
    `${req.method} ${req.url} - Origin: ${req.headers.origin || 'No origin'}`
  );
  next();
});

// // Health check endpoint
// app.get('/health', async (req, res) => {
//   try {
//     const healthResult = await healthCheck();

//     const isHealthy = healthResult.database === 'connected';
//     const statusCode = isHealthy
//       ? status.STATUS_CODE_SUCCESS
//       : status.STATUS_CODE_SERVICE_UNAVAILABLE;

//     res.status(statusCode).json({
//       success: isHealthy,
//       message: isHealthy
//         ? 'Server is healthy'
//         : 'Server health check failed - database disconnected',
//       timestamp: healthResult.timestamp,
//       database: healthResult.database,
//       ...(isHealthy
//         ? { status: healthResult.status }
//         : { error: healthResult.connection.error }),
//     });
//   } catch (error) {
//     logger.error('Health check failed:', error);
//     res.status(status.STATUS_CODE_INTERNAL_SERVER_STATUS).json({
//       success: false,
//       message: 'Server health check failed',
//       timestamp: new Date().toISOString(),
//       database: 'error',
//       error: error.message,
//     });
//   }
// });

// Root endpoint
app.get('/', (req, res) => {
  res.status(status.STATUS_CODE_SUCCESS).send(CONSTANTS.AUTH.HELLO_WORLD);
});

// API routes
app.use('/api', mainRoutes);

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error(`Error: ${err.message}`);

  // Handle CORS errors specifically
  if (err.message === 'Not allowed by CORS') {
    return res.status(403).json({
      message: 'CORS policy violation',
      error: 'Origin not allowed',
    });
  }

  res.status(500).json({ message: CONSTANTS.USER.INTERNAL_SERVER_ERROR });
});

// 404 handler for unmatched routes
app.use('*', (req, res) => {
  res.status(404).json({
    message: 'Route not found',
    path: req.originalUrl,
  });
});

export { app };
