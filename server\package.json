{"name": "mobio-node-init", "version": "1.0.0", "type": "module", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "migrate:undo:all": "npx sequelize-cli db:migrate:undo:all", "migrate:status": "npx sequelize-cli db:migrate:status", "seed": "npx sequelize-cli db:seed:all", "seed:undo": "npx sequelize-cli db:seed:undo", "seed:undo:all": "npx sequelize-cli db:seed:undo:all", "seed:roles": "npx sequelize-cli db:seed --seed roles.seeder.js", "seed:users": "npx sequelize-cli db:seed --seed users.seeder.js", "db:reset": "npm run migrate:undo:all && npm run migrate && npm run seed", "db:setup": "npm run migrate && npm run seed", "db:test": "node test-connection.js", "lint": "eslint .", "lint:fix": "eslint --fix .", "format": "prettier --write .", "test": "jest --coverage"}, "author": "Mobio Solutions", "license": "ISC", "dependencies": {"@azure/msal-node": "^2.16.2", "@azure/storage-blob": "^12.27.0", "@babel/preset-env": "^7.24.7", "@bull-board/api": "^6.9.1", "@bull-board/express": "^6.9.1", "@supabase/supabase-js": "^2.49.1", "amqplib": "^0.10.7", "aws-sdk": "^2.1692.0", "babel-jest": "^29.7.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "bullmq": "^5.49.1", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "fast-csv": "^5.0.2", "helmet": "^8.0.0", "http-proxy-middleware": "^3.0.5", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.3.4", "morgan": "^1.10.0", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "nodemailer": "^6.9.13", "nodemon": "^3.1.0", "passport": "^0.7.0", "passport-auth0": "^1.4.4", "pdfkit": "^0.17.0", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "redis": "^4.7.0", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "winston": "^3.13.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@commitlint/config-conventional": "^19.8.0", "commitlint": "^19.8.0", "eslint": "^9.3.0", "globals": "^15.3.0", "jest": "^29.7.0", "prettier": "^3.2.5", "supertest": "^7.0.0"}}