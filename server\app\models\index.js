import { sequelize } from '../config/aws-config.js';
import TenantModel from './tenant.model.js';
import OrganizationModel from './organization.model.js';
import RoleModel from './roles.model.js';
import UserModel from './user.model.js';
import PermissionModel from './permission.model.js';
import TokenModel from './token.model.js';

// Initialize models
const Tenant = TenantModel(sequelize);
const Organization = OrganizationModel(sequelize);
const Role = RoleModel(sequelize);
const User = UserModel(sequelize);
const Permission = PermissionModel(sequelize);
const Token = TokenModel(sequelize);

// User - Role relationship
Role.hasMany(User, { foreignKey: 'role_id' });
User.belongsTo(Role, { foreignKey: 'role_id' });

// User - Tenant relationship
Tenant.hasMany(User, { foreignKey: 'tenant_id' });
User.belongsTo(Tenant, { foreignKey: 'tenant_id' });

// User - Organization relationship
Organization.hasMany(User, { foreignKey: 'organization_id' });
User.belongsTo(Organization, { foreignKey: 'organization_id' });

// User - Token relationship
User.hasMany(Token, { foreignKey: 'userId' });
Token.belongsTo(User, { foreignKey: 'userId' });

// Self-referencing User audit fields
User.hasMany(User, { as: 'CreatedUsers', foreignKey: 'created_by' });
User.belongsTo(User, { as: 'CreatedBy', foreignKey: 'created_by' });

User.hasMany(User, { as: 'UpdatedUsers', foreignKey: 'updated_by' });
User.belongsTo(User, { as: 'UpdatedBy', foreignKey: 'updated_by' });

// Token audit fields
Token.belongsTo(User, { as: 'CreatedBy', foreignKey: 'createdBy' });
Token.belongsTo(User, { as: 'UpdatedBy', foreignKey: 'updatedBy' });

export {
  sequelize,
  Tenant,
  Organization,
  Role,
  User,
  Permission,
  Token,
};
