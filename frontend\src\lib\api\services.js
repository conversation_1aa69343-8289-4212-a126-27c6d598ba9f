import { BaseAPIService } from "./base.js";
import { ROUTES } from "@/utils/const/routes";
import api from "../axios.js";

/**
 * User API Service
 */
export class UserAPIService extends BaseAPIService {
  constructor() {
    super(ROUTES.API.USERS);
  }

  /**
   * Get user profile
   * @param {string} userId - User ID
   * @returns {Promise} User profile data
   */
  async getProfile(userId) {
    const response = await api.get(`${this.endpoint}/${userId}/profile`);
    return response.data;
  }

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} profileData - Profile data
   * @returns {Promise} Updated profile data
   */
  async updateProfile(userId, profileData) {
    const response = await api.put(`${this.endpoint}/${userId}/profile`, profileData);
    return response.data;
  }

  /**
   * Change user password
   * @param {string} userId - User ID
   * @param {Object} passwordData - Password change data
   * @returns {Promise} API response
   */
  async changePassword(userId, passwordData) {
    const response = await api.put(`${this.endpoint}/${userId}/password`, passwordData);
    return response.data;
  }

  /**
   * Get user roles
   * @param {string} userId - User ID
   * @returns {Promise} User roles
   */
  async getUserRoles(userId) {
    const response = await api.get(`${this.endpoint}/${userId}/roles`);
    return response.data;
  }

  /**
   * Assign roles to user
   * @param {string} userId - User ID
   * @param {Array} roleIds - Array of role IDs
   * @returns {Promise} API response
   */
  async assignRoles(userId, roleIds) {
    const response = await api.post(`${this.endpoint}/${userId}/roles`, { roleIds });
    return response.data;
  }

  /**
   * Get users by organization
   * @param {string} organizationId - Organization ID
   * @returns {Promise} Users in organization
   */
  async getByOrganization(organizationId) {
    const response = await api.get(`${this.endpoint}/organization/${organizationId}`);
    return response.data;
  }
}

/**
 * Tenant API Service
 */
export class TenantAPIService extends BaseAPIService {
  constructor() {
    super(ROUTES.API.TENANTS);
  }

  /**
   * Get tenant settings
   * @param {string} tenantId - Tenant ID
   * @returns {Promise} Tenant settings
   */
  async getSettings(tenantId) {
    const response = await api.get(`${this.endpoint}/${tenantId}/settings`);
    return response.data;
  }

  /**
   * Update tenant settings
   * @param {string} tenantId - Tenant ID
   * @param {Object} settings - Settings data
   * @returns {Promise} Updated settings
   */
  async updateSettings(tenantId, settings) {
    const response = await api.put(`${this.endpoint}/${tenantId}/settings`, settings);
    return response.data;
  }

  /**
   * Get tenant statistics
   * @param {string} tenantId - Tenant ID
   * @returns {Promise} Tenant statistics
   */
  async getStatistics(tenantId) {
    const response = await api.get(`${this.endpoint}/${tenantId}/statistics`);
    return response.data;
  }

  /**
   * Activate tenant
   * @param {string} tenantId - Tenant ID
   * @returns {Promise} API response
   */
  async activate(tenantId) {
    const response = await api.post(`${this.endpoint}/${tenantId}/activate`);
    return response.data;
  }

  /**
   * Deactivate tenant
   * @param {string} tenantId - Tenant ID
   * @returns {Promise} API response
   */
  async deactivate(tenantId) {
    const response = await api.post(`${this.endpoint}/${tenantId}/deactivate`);
    return response.data;
  }
}

/**
 * Organization API Service
 */
export class OrganizationAPIService extends BaseAPIService {
  constructor() {
    super(ROUTES.API.ORGANIZATIONS);
  }

  /**
   * Get organization hierarchy
   * @param {string} organizationId - Organization ID
   * @returns {Promise} Organization hierarchy
   */
  async getHierarchy(organizationId) {
    const response = await api.get(`${this.endpoint}/${organizationId}/hierarchy`);
    return response.data;
  }

  /**
   * Get organization members
   * @param {string} organizationId - Organization ID
   * @returns {Promise} Organization members
   */
  async getMembers(organizationId) {
    const response = await api.get(`${this.endpoint}/${organizationId}/members`);
    return response.data;
  }

  /**
   * Add member to organization
   * @param {string} organizationId - Organization ID
   * @param {string} userId - User ID
   * @param {string} role - User role in organization
   * @returns {Promise} API response
   */
  async addMember(organizationId, userId, role) {
    const response = await api.post(`${this.endpoint}/${organizationId}/members`, {
      userId,
      role,
    });
    return response.data;
  }

  /**
   * Remove member from organization
   * @param {string} organizationId - Organization ID
   * @param {string} userId - User ID
   * @returns {Promise} API response
   */
  async removeMember(organizationId, userId) {
    const response = await api.delete(`${this.endpoint}/${organizationId}/members/${userId}`);
    return response.data;
  }
}

/**
 * Role API Service
 */
export class RoleAPIService extends BaseAPIService {
  constructor() {
    super(ROUTES.API.ROLES);
  }

  /**
   * Get role permissions
   * @param {string} roleId - Role ID
   * @returns {Promise} Role permissions
   */
  async getPermissions(roleId) {
    const response = await api.get(`${this.endpoint}/${roleId}/permissions`);
    return response.data;
  }

  /**
   * Assign permissions to role
   * @param {string} roleId - Role ID
   * @param {Array} permissionIds - Array of permission IDs
   * @returns {Promise} API response
   */
  async assignPermissions(roleId, permissionIds) {
    const response = await api.post(`${this.endpoint}/${roleId}/permissions`, {
      permissionIds,
    });
    return response.data;
  }

  /**
   * Remove permissions from role
   * @param {string} roleId - Role ID
   * @param {Array} permissionIds - Array of permission IDs
   * @returns {Promise} API response
   */
  async removePermissions(roleId, permissionIds) {
    const response = await api.delete(`${this.endpoint}/${roleId}/permissions`, {
      data: { permissionIds },
    });
    return response.data;
  }

  /**
   * Get users with role
   * @param {string} roleId - Role ID
   * @returns {Promise} Users with role
   */
  async getUsers(roleId) {
    const response = await api.get(`${this.endpoint}/${roleId}/users`);
    return response.data;
  }
}

/**
 * Permission API Service
 */
export class PermissionAPIService extends BaseAPIService {
  constructor() {
    super(ROUTES.API.PERMISSIONS);
  }

  /**
   * Get permissions by module
   * @param {string} module - Module name
   * @returns {Promise} Module permissions
   */
  async getByModule(module) {
    const response = await api.get(`${this.endpoint}/module/${module}`);
    return response.data;
  }

  /**
   * Get permission tree
   * @returns {Promise} Permission tree structure
   */
  async getTree() {
    const response = await api.get(`${this.endpoint}/tree`);
    return response.data;
  }

  /**
   * Check user permission
   * @param {string} userId - User ID
   * @param {string} permission - Permission name
   * @returns {Promise} Permission check result
   */
  async checkUserPermission(userId, permission) {
    const response = await api.get(`${this.endpoint}/check/${userId}/${permission}`);
    return response.data;
  }
}

/**
 * File API Service
 */
export class FileAPIService extends BaseAPIService {
  constructor() {
    super("/api/files");
  }

  /**
   * Upload file
   * @param {File} file - File to upload
   * @param {Object} options - Upload options
   * @returns {Promise} Upload result
   */
  async upload(file, options = {}) {
    const formData = new FormData();
    formData.append("file", file);
    
    Object.keys(options).forEach(key => {
      formData.append(key, options[key]);
    });

    const response = await api.post(`${this.endpoint}/upload`, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: options.onProgress,
    });
    return response.data;
  }

  /**
   * Download file
   * @param {string} fileId - File ID
   * @returns {Promise} File blob
   */
  async download(fileId) {
    const response = await api.get(`${this.endpoint}/${fileId}/download`, {
      responseType: "blob",
    });
    return response;
  }

  /**
   * Get file metadata
   * @param {string} fileId - File ID
   * @returns {Promise} File metadata
   */
  async getMetadata(fileId) {
    const response = await api.get(`${this.endpoint}/${fileId}/metadata`);
    return response.data;
  }

  /**
   * Delete file
   * @param {string} fileId - File ID
   * @returns {Promise} API response
   */
  async deleteFile(fileId) {
    const response = await api.delete(`${this.endpoint}/${fileId}`);
    return response.data;
  }
}

// Create service instances
export const userAPI = new UserAPIService();
export const tenantAPI = new TenantAPIService();
export const organizationAPI = new OrganizationAPIService();
export const roleAPI = new RoleAPIService();
export const permissionAPI = new PermissionAPIService();
export const fileAPI = new FileAPIService();

// Export all services
export const apiServices = {
  user: userAPI,
  tenant: tenantAPI,
  organization: organizationAPI,
  role: roleAPI,
  permission: permissionAPI,
  file: fileAPI,
};
