// Export all API services and utilities from a central location

// Base API utilities
export {
  BaseAPIService,
  APIResponse,
  handleAPIError,
  createAPIService,
  withErrorHandling,
  withRetry,
} from "./base.js";

// Authentication API
export { authAPI } from "./auth.js";

// Specific API services
export {
  UserAPIService,
  TenantAPIService,
  OrganizationAPIService,
  RoleAPIService,
  PermissionAPIService,
  FileAPIService,
  userAPI,
  tenantAPI,
  organizationAPI,
  roleAPI,
  permissionAPI,
  fileAPI,
  apiServices,
} from "./services.js";

// Axios instance
export { default as api } from "../axios.js";

/**
 * API configuration and utilities
 */
export const API_CONFIG = {
  // Default pagination settings
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
  
  // Request timeouts
  DEFAULT_TIMEOUT: 10000,
  UPLOAD_TIMEOUT: 60000,
  
  // Retry settings
  DEFAULT_RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  
  // File upload settings
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_FILE_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ],
};

/**
 * Common API response status codes
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
};

/**
 * API error types
 */
export const API_ERROR_TYPES = {
  NETWORK_ERROR: 'NETWORK_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
};

/**
 * Get error type from HTTP status
 * @param {number} status - HTTP status code
 * @returns {string} Error type
 */
export const getErrorType = (status) => {
  switch (status) {
    case HTTP_STATUS.BAD_REQUEST:
    case HTTP_STATUS.UNPROCESSABLE_ENTITY:
      return API_ERROR_TYPES.VALIDATION_ERROR;
    case HTTP_STATUS.UNAUTHORIZED:
      return API_ERROR_TYPES.AUTHENTICATION_ERROR;
    case HTTP_STATUS.FORBIDDEN:
      return API_ERROR_TYPES.AUTHORIZATION_ERROR;
    case HTTP_STATUS.NOT_FOUND:
      return API_ERROR_TYPES.NOT_FOUND_ERROR;
    case HTTP_STATUS.INTERNAL_SERVER_ERROR:
    case HTTP_STATUS.BAD_GATEWAY:
    case HTTP_STATUS.SERVICE_UNAVAILABLE:
      return API_ERROR_TYPES.SERVER_ERROR;
    case 0:
      return API_ERROR_TYPES.NETWORK_ERROR;
    default:
      return API_ERROR_TYPES.SERVER_ERROR;
  }
};

/**
 * Format API error for display
 * @param {Object} error - Error object
 * @returns {Object} Formatted error
 */
export const formatAPIError = (error) => {
  const errorInfo = handleAPIError(error);
  const errorType = getErrorType(errorInfo.status);
  
  return {
    type: errorType,
    status: errorInfo.status,
    message: errorInfo.message,
    errors: errorInfo.errors,
    timestamp: new Date().toISOString(),
  };
};

/**
 * Check if error is retryable
 * @param {Object} error - Error object
 * @returns {boolean} Whether error is retryable
 */
export const isRetryableError = (error) => {
  const status = error.response?.status;
  
  // Don't retry client errors (4xx)
  if (status >= 400 && status < 500) {
    return false;
  }
  
  // Retry server errors (5xx) and network errors
  return true;
};

/**
 * Create query string from parameters
 * @param {Object} params - Query parameters
 * @returns {string} Query string
 */
export const createQueryString = (params) => {
  const searchParams = new URLSearchParams();
  
  Object.keys(params).forEach(key => {
    const value = params[key];
    if (value !== null && value !== undefined && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, item));
      } else {
        searchParams.append(key, value);
      }
    }
  });
  
  return searchParams.toString();
};

/**
 * Parse API response for pagination
 * @param {Object} response - API response
 * @returns {Object} Parsed pagination data
 */
export const parsePaginationResponse = (response) => {
  return {
    data: response.data || [],
    currentPage: response.currentPage || 1,
    totalPages: response.totalPages || 1,
    totalItems: response.totalItems || 0,
    pageSize: response.pageSize || API_CONFIG.DEFAULT_PAGE_SIZE,
    hasNextPage: response.hasNextPage || false,
    hasPrevPage: response.hasPrevPage || false,
  };
};

/**
 * Validate file before upload
 * @param {File} file - File to validate
 * @param {Object} options - Validation options
 * @returns {Object} Validation result
 */
export const validateFile = (file, options = {}) => {
  const {
    maxSize = API_CONFIG.MAX_FILE_SIZE,
    allowedTypes = API_CONFIG.ALLOWED_FILE_TYPES,
  } = options;
  
  const errors = [];
  
  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`);
  }
  
  // Check file type
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};
